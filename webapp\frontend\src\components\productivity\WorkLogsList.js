import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Paper,
  Chip,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
  Badge,
  LinearProgress,
  Pagination,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Assignment,
  Person,
  Schedule,
  Engineering,
  TrendingUp,
  FilterList,
  Clear,
  Edit,
  Delete,
  Visibility,
  Add,
  ExpandMore,
  Speed,
  Timer,
  Group,
  LocationOn,
  Build,
  Assessment,
  Construction
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import axiosInstance from '../../services/axiosConfig';

const WorkLogsList = ({ onEdit, onDelete, onCreateNew }) => {
  const { selectedCantiere: authSelectedCantiere } = useAuth();
  const [workLogs, setWorkLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    activity_type: '',
    operator_id: '',
    start_date: '',
    end_date: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 20,
    total_count: 0
  });
  const [operators, setOperators] = useState([]);

  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage
  const currentCantiereId = authSelectedCantiere?.id_cantiere ||
                           parseInt(localStorage.getItem('selectedCantiereId'), 10) ||
                           null;
  const currentCantiereName = authSelectedCantiere?.commessa ||
                             localStorage.getItem('selectedCantiereName') ||
                             'Cantiere non selezionato';

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (currentCantiereId) {
      loadWorkLogs();
    }
  }, [currentCantiereId, filters, pagination.page]);

  // Carica i dati solo se c'è un cantiere attivo
  useEffect(() => {
    if (!currentCantiereId) {
      setLoading(false);
    }
  }, [currentCantiereId]);

  const loadInitialData = async () => {
    try {
      const operatorsRes = await axiosInstance.get('/responsabili');
      setOperators(operatorsRes.data || []);
    } catch (error) {
      console.error('Errore nel caricamento dati iniziali:', error);
    }
  };

  const loadWorkLogs = async () => {
    if (!currentCantiereId) {
      console.warn('Nessun cantiere attivo selezionato per i work logs');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Parametri per le chiamate API - usa solo il cantiere attivo
      const params = {
        page: pagination.page,
        per_page: pagination.per_page,
        id_cantiere: currentCantiereId, // Forza il cantiere attivo
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      };

      console.log('Caricamento work logs per cantiere:', currentCantiereId, currentCantiereName);

      const response = await axiosInstance.get('/v1/work-logs', { params });

      setWorkLogs(response.data.work_logs || []);
      setPagination(prev => ({
        ...prev,
        total_count: response.data.total_count || 0
      }));

    } catch (error) {
      console.error('Errore nel caricamento work logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset alla prima pagina
  };

  const clearFilters = () => {
    setFilters({
      activity_type: '',
      operator_id: '',
      start_date: '',
      end_date: ''
    });
  };

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString('it-IT', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (minutes) => {
    if (!minutes) return '-';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getActivityIcon = (activity) => {
    switch (activity) {
      case 'Posa': return '🔧';
      case 'Collegamento': return '🔌';
      case 'Certificazione': return '✅';
      default: return '📝';
    }
  };

  const getConditionColor = (condition) => {
    switch (condition) {
      case 'Normale': return 'bg-green-100 text-green-800';
      case 'Spazi Ristretti': return 'bg-yellow-100 text-yellow-800';
      case 'In Altezza': return 'bg-orange-100 text-orange-800';
      case 'Esterno': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const totalPages = Math.ceil(pagination.total_count / pagination.per_page);

  if (loading && workLogs.length === 0) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <CircularProgress sx={{ mb: 2 }} />
        <Typography variant="h6" color="text.secondary">
          Caricamento work logs...
        </Typography>
      </Box>
    );
  }

  // Se non c'è un cantiere attivo, mostra un messaggio
  if (!currentCantiereId) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            Nessun cantiere attivo selezionato
          </Typography>
          <Typography variant="body2">
            Per visualizzare e gestire i work logs, seleziona prima un cantiere dalla dashboard principale.
          </Typography>
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header moderno con cantiere attivo */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Assignment sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 700 }}>
                Work Logs
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Construction sx={{ fontSize: 20, color: 'text.secondary', mr: 1 }} />
                <Typography variant="subtitle1" color="text.secondary">
                  Cantiere: <strong>{currentCantiereName}</strong>
                </Typography>
                <Chip
                  label={`ID: ${currentCantiereId}`}
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{ ml: 2 }}
                />
              </Box>
            </Box>
          </Box>

          <Button
            variant="contained"
            onClick={onCreateNew}
            startIcon={<Add />}
            size="large"
            sx={{
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
              '&:hover': {
                background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',
              }
            }}
          >
            Nuovo Work Log
          </Button>
        </Box>

        {/* KPI Cards moderne */}
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card
              elevation={3}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                position: 'relative',
                overflow: 'visible'
              }}
            >
              <CardContent sx={{ pb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
                      Totale Work Logs
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      {pagination.total_count}
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                    <Assignment sx={{ fontSize: 28 }} />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card
              elevation={3}
              sx={{
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                color: 'white'
              }}
            >
              <CardContent sx={{ pb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
                      Quantità Totale
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      {workLogs.reduce((sum, log) => sum + (log.quantity || 0), 0).toFixed(1)}
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.8 }}>
                      metri/unità
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                    <TrendingUp sx={{ fontSize: 28 }} />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card
              elevation={3}
              sx={{
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                color: 'white'
              }}
            >
              <CardContent sx={{ pb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
                      Ore-Uomo Totali
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      {workLogs.reduce((sum, log) => sum + (log.total_man_hours || 0), 0).toFixed(1)}
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.8 }}>
                      ore lavorate
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                    <Schedule sx={{ fontSize: 28 }} />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card
              elevation={3}
              sx={{
                background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                color: 'white'
              }}
            >
              <CardContent sx={{ pb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
                      Produttività Media
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      {workLogs.length > 0 ?
                        (workLogs.reduce((sum, log) => sum + (log.productivity_per_hour || 0), 0) / workLogs.length).toFixed(2)
                        : '0.00'
                      }
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.8 }}>
                      unità/ora
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                    <Speed sx={{ fontSize: 28 }} />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Filtri moderni */}
      <Accordion elevation={3} sx={{ mb: 3 }}>
        <AccordionSummary
          expandIcon={<ExpandMore />}
          sx={{
            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
            }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <FilterList sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Filtri Avanzati
            </Typography>
            <Box sx={{ ml: 'auto', mr: 2 }}>
              {Object.values(filters).some(v => v !== '') && (
                <Chip
                  label="Filtri Attivi"
                  color="primary"
                  size="small"
                  variant="outlined"
                />
              )}
            </Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6} lg={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Attività</InputLabel>
                <Select
                  name="activity_type"
                  value={filters.activity_type}
                  onChange={handleFilterChange}
                  label="Attività"
                >
                  <MenuItem value="">Tutte</MenuItem>
                  <MenuItem value="Posa">🔧 Posa</MenuItem>
                  <MenuItem value="Collegamento">🔌 Collegamento</MenuItem>
                  <MenuItem value="Certificazione">✅ Certificazione</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6} lg={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Operatore</InputLabel>
                <Select
                  name="operator_id"
                  value={filters.operator_id}
                  onChange={handleFilterChange}
                  label="Operatore"
                >
                  <MenuItem value="">Tutti</MenuItem>
                  {operators.map(op => (
                    <MenuItem key={op.id_responsabile} value={op.id_responsabile}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Person sx={{ mr: 1, fontSize: 16 }} />
                        {op.nome_responsabile}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>



            <Grid item xs={12} md={6} lg={3}>
              <TextField
                fullWidth
                size="small"
                type="date"
                name="start_date"
                label="Data Inizio"
                value={filters.start_date}
                onChange={handleFilterChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} md={6} lg={3}>
              <TextField
                fullWidth
                size="small"
                type="date"
                name="end_date"
                label="Data Fine"
                value={filters.end_date}
                onChange={handleFilterChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <Button
                  variant="outlined"
                  onClick={clearFilters}
                  startIcon={<Clear />}
                  size="small"
                >
                  Pulisci Filtri
                </Button>
              </Box>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Lista Work Logs con Cards moderne */}
      {workLogs.length === 0 ? (
        <Paper elevation={3} sx={{ p: 8, textAlign: 'center' }}>
          <Assignment sx={{ fontSize: 80, mb: 2, opacity: 0.3, color: 'text.secondary' }} />
          <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
            Nessun work log trovato
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            {Object.values(filters).some(v => v !== '')
              ? 'Nessun risultato per i filtri selezionati. Prova a modificare i criteri di ricerca.'
              : 'Inizia creando il tuo primo work log per monitorare la produttività.'
            }
          </Typography>
          <Button
            variant="contained"
            onClick={onCreateNew}
            startIcon={<Add />}
            size="large"
            sx={{
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
              '&:hover': {
                background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',
              }
            }}
          >
            Crea il primo Work Log
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {workLogs.map((log) => (
            <Grid item xs={12} md={6} lg={4} key={log.id}>
              <Card
                elevation={3}
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                  }
                }}
              >
                <CardHeader
                  avatar={
                    <Avatar
                      sx={{
                        bgcolor: log.activity_type === 'Posa' ? 'primary.main' :
                                log.activity_type === 'Collegamento' ? 'secondary.main' : 'success.main',
                        width: 48,
                        height: 48
                      }}
                    >
                      {log.activity_type === 'Posa' ? <Engineering /> :
                       log.activity_type === 'Collegamento' ? <Build /> : <Assessment />}
                    </Avatar>
                  }
                  title={
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {log.activity_type}
                    </Typography>
                  }
                  subheader={
                    <Typography variant="body2" color="text.secondary">
                      {log.sub_activity_detail || 'Dettaglio non specificato'}
                    </Typography>
                  }
                  action={
                    <Chip
                      label={formatDateTime(log.start_timestamp)}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  }
                />

                <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                  {/* Informazioni principali */}
                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={6}>
                      <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'primary.50', borderRadius: 1 }}>
                        <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.main' }}>
                          {log.quantity?.toFixed(1)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {log.activity_type === 'Posa' ? 'metri' : 'unità'}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box sx={{ textAlign: 'center', p: 1, bgcolor: 'success.50', borderRadius: 1 }}>
                        <Typography variant="h6" sx={{ fontWeight: 700, color: 'success.main' }}>
                          {formatDuration(log.duration_minutes)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          durata
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  {/* Dettagli operatore e produttività */}
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Person sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        Operatore #{log.operator_id}
                      </Typography>
                      <Chip
                        label={`${log.number_of_operators_on_task} op`}
                        size="small"
                        sx={{ ml: 'auto' }}
                      />
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Speed sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {log.productivity_per_hour?.toFixed(2)} /h
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                        ({log.productivity_per_person_per_hour?.toFixed(2)} /h/op)
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <LocationOn sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {log.environmental_conditions} • {log.tools_used}
                      </Typography>
                    </Box>
                  </Box>

                  {/* Progress bar produttività */}
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        Efficienza
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {Math.min(100, (log.productivity_per_person_per_hour / 100) * 100).toFixed(0)}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={Math.min(100, (log.productivity_per_person_per_hour / 100) * 100)}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        bgcolor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 3,
                          background: log.productivity_per_person_per_hour > 80
                            ? 'linear-gradient(90deg, #4caf50, #8bc34a)'
                            : log.productivity_per_person_per_hour > 50
                            ? 'linear-gradient(90deg, #ff9800, #ffc107)'
                            : 'linear-gradient(90deg, #f44336, #ff5722)'
                        }
                      }}
                    />
                  </Box>

                  {/* Note se presenti */}
                  {log.notes && (
                    <Box sx={{ p: 1, bgcolor: 'grey.50', borderRadius: 1, mb: 2 }}>
                      <Typography variant="caption" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                        "{log.notes}"
                      </Typography>
                    </Box>
                  )}

                  {/* Azioni */}
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                    <Tooltip title="Modifica">
                      <IconButton
                        onClick={() => onEdit(log)}
                        color="primary"
                        size="small"
                      >
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Elimina">
                      <IconButton
                        onClick={() => onDelete(log.id)}
                        color="error"
                        size="small"
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Paginazione moderna */}
      {totalPages > 1 && (
        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                Mostrando {(pagination.page - 1) * pagination.per_page + 1}-
                {Math.min(pagination.page * pagination.per_page, pagination.total_count)} di {pagination.total_count}
              </Typography>

              <Pagination
                count={totalPages}
                page={pagination.page}
                onChange={(event, value) => setPagination(prev => ({ ...prev, page: value }))}
                color="primary"
                variant="outlined"
                shape="rounded"
                showFirstButton
                showLastButton
              />
            </Stack>
          </Paper>
        </Box>
      )}
    </Box>
  );
};

export default WorkLogsList;
