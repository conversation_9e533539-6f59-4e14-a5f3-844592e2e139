#!/usr/bin/env python3
"""
Script per testare le API del sistema di produttività.
"""

import requests
import json
from datetime import datetime, timedelta
import time

# Configurazione
BASE_URL = "http://localhost:8001/api"
USERNAME = "admin"
PASSWORD = "admin"

def login():
    """Effettua il login e restituisce il token."""
    try:
        login_data = {
            "username": USERNAME,
            "password": PASSWORD
        }
        
        print(f"🔐 Login con {USERNAME}...")
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data, timeout=10)
        
        if response.status_code == 200:
            token = response.json().get("access_token")
            print(f"✅ Login riuscito!")
            return token
        else:
            print(f"❌ Login fallito: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Errore durante il login: {str(e)}")
        return None

def test_work_logs_api(token):
    """Testa le API dei work logs."""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\n🧪 TEST API WORK LOGS")
    print("=" * 50)
    
    # Test 1: Crea un work log
    print("\n📝 Test 1: Creazione work log")
    
    # Prima ottieni un responsabile esistente
    try:
        response = requests.get(f"{BASE_URL}/responsabili/cantiere/1", headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            # La risposta potrebbe essere una lista diretta o un oggetto con 'responsabili'
            if isinstance(data, list):
                responsabili = data
            else:
                responsabili = data.get('responsabili', [])

            if responsabili:
                operator_id = responsabili[0]['id_responsabile']
                print(f"✅ Trovato operatore ID: {operator_id}")
            else:
                print("⚠️ Nessun responsabile trovato, uso ID 1")
                operator_id = 1
        else:
            print("⚠️ Errore nel recupero responsabili, uso ID 1")
            operator_id = 1
    except Exception as e:
        print(f"⚠️ Errore nel recupero responsabili: {e}, uso ID 1")
        operator_id = 1
    
    # Crea work log di test
    now = datetime.now()
    start_time = now - timedelta(hours=2)
    end_time = now - timedelta(hours=1)
    
    work_log_data = {
        "operator_id": operator_id,
        "cable_type_id": None,  # Opzionale
        "activity_type": "Posa",
        "sub_activity_detail": "Posa in canalina a vista",
        "environmental_conditions": "Normale",
        "tools_used": "Manuale",
        "quantity": 150.5,  # metri
        "start_timestamp": start_time.isoformat(),
        "end_timestamp": end_time.isoformat(),
        "number_of_operators_on_task": 2,
        "notes": "Test work log creato automaticamente",
        "id_cantiere": 1
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/work-logs",
            headers=headers,
            json=work_log_data,
            timeout=10
        )
        
        if response.status_code == 201:
            work_log = response.json()
            work_log_id = work_log['id']
            print(f"✅ Work log creato con ID: {work_log_id}")
            print(f"   Produttività: {work_log.get('productivity_per_hour', 'N/A')} m/h")
            print(f"   Ore-uomo: {work_log.get('total_man_hours', 'N/A')}")
        else:
            print(f"❌ Errore creazione work log: {response.status_code}")
            print(f"   Dettagli: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Errore nella richiesta: {str(e)}")
        return False
    
    # Test 2: Lista work logs
    print("\n📋 Test 2: Lista work logs")
    
    try:
        response = requests.get(
            f"{BASE_URL}/v1/work-logs?id_cantiere=1&per_page=5", 
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Trovati {data['total_count']} work logs")
            print(f"   Pagina {data['page']} di {data['per_page']} per pagina")
            
            for wl in data['work_logs'][:3]:  # Mostra primi 3
                print(f"   - ID {wl['id']}: {wl['activity_type']} - {wl['quantity']} unità")
        else:
            print(f"❌ Errore lista work logs: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Errore nella richiesta: {str(e)}")
    
    return True

def test_productivity_historical_api(token):
    """Testa l'API di produttività storica."""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\n📊 Test 3: Produttività storica")
    
    try:
        # Test con filtri
        params = {
            "activity_type": "Posa",
            "id_cantiere": 1
        }
        
        response = requests.get(
            f"{BASE_URL}/v1/productivity/historical", 
            headers=headers,
            params=params,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Produttività calcolata: {data['calculated_productivity']:.2f} {data['unit']}")
            print(f"   Basata su {data['number_of_logs']} log")
            print(f"   Quantità totale: {data['total_quantity']}")
            print(f"   Ore-uomo totali: {data['total_man_hours']:.2f}")
        elif response.status_code == 404:
            print("⚠️ Nessun dato storico trovato (normale per nuovo sistema)")
        else:
            print(f"❌ Errore produttività storica: {response.status_code}")
            print(f"   Dettagli: {response.text}")
            
    except Exception as e:
        print(f"❌ Errore nella richiesta: {str(e)}")

def test_prediction_api(token):
    """Testa l'API di predizione."""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\n🔮 Test 4: Stima predittiva")
    
    prediction_data = {
        "activity_type": "Posa",
        "quantity_required": 200.0,  # metri
        "environmental_conditions": "Spazi Ristretti",
        "tools_used": "Manuale",
        "number_of_operators": 3,
        "experience_level": "Senior"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/predict/estimation", 
            headers=headers,
            json=prediction_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Stima completata!")
            print(f"   Produttività base: {data['base_productivity']:.2f}")
            print(f"   Produttività attesa: {data['expected_productivity_per_operator']:.2f}")
            print(f"   Ore-uomo stimate: {data['estimated_total_man_hours']:.2f}")
            print(f"   Tempo team: {data['estimated_time_for_team_hours']:.2f} ore")
            print(f"   Fattori correzione: {data['correction_factors']}")
        else:
            print(f"❌ Errore stima predittiva: {response.status_code}")
            print(f"   Dettagli: {response.text}")
            
    except Exception as e:
        print(f"❌ Errore nella richiesta: {str(e)}")

def main():
    """Funzione principale."""
    print("🧪 TEST SISTEMA PRODUTTIVITÀ API")
    print("=" * 60)
    
    # Step 1: Login
    token = login()
    if not token:
        print("\n❌ Impossibile procedere senza autenticazione")
        return False
    
    # Step 2: Test work logs
    if not test_work_logs_api(token):
        print("\n❌ Test work logs fallito")
        return False
    
    # Step 3: Test produttività storica
    test_productivity_historical_api(token)
    
    # Step 4: Test predizione
    test_prediction_api(token)
    
    print("\n🎉 TEST COMPLETATI!")
    print("=" * 60)
    print("✅ Il sistema di produttività è funzionante")
    print("\n📚 PROSSIMI PASSI:")
    print("1. Implementare il frontend React")
    print("2. Creare dashboard di visualizzazione")
    print("3. Aggiungere machine learning (opzionale)")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Test falliti")
        exit(1)
    else:
        print("\n✅ Tutti i test superati!")
