{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport axiosInstance from '../../services/axiosConfig';\n\n// Registra i componenti Chart.js\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement);\nconst Dashboard = () => {\n  _s();\n  var _statistics$totalQuan, _statistics$totalManH, _statistics$averagePr;\n  const [workLogs, setWorkLogs] = useState([]);\n  const [statistics, setStatistics] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [selectedCantiere, setSelectedCantiere] = useState('');\n  const [cantieri, setCantieri] = useState([]);\n  const [dateRange, setDateRange] = useState({\n    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n    // 30 giorni fa\n    end: new Date().toISOString().split('T')[0] // oggi\n  });\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n  useEffect(() => {\n    if (cantieri.length > 0) {\n      loadDashboardData();\n    }\n  }, [selectedCantiere, dateRange, cantieri]);\n  const loadInitialData = async () => {\n    try {\n      const cantieriRes = await axiosInstance.get('/cantieri');\n      setCantieri(cantieriRes.data || []);\n\n      // Seleziona il primo cantiere automaticamente\n      if (cantieriRes.data && cantieriRes.data.length > 0) {\n        setSelectedCantiere(cantieriRes.data[0].id_cantiere.toString());\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento cantieri:', error);\n    }\n  };\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Parametri per le chiamate API\n      const params = {\n        id_cantiere: selectedCantiere,\n        start_date: dateRange.start,\n        end_date: dateRange.end,\n        per_page: 100\n      };\n\n      // Carica work logs\n      const workLogsRes = await axiosInstance.get('/v1/work-logs', {\n        params\n      });\n      setWorkLogs(workLogsRes.data.work_logs || []);\n\n      // Calcola statistiche\n      calculateStatistics(workLogsRes.data.work_logs || []);\n    } catch (error) {\n      console.error('Errore nel caricamento dati dashboard:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const calculateStatistics = logs => {\n    const stats = {\n      totalLogs: logs.length,\n      totalQuantity: 0,\n      totalManHours: 0,\n      averageProductivity: 0,\n      byActivity: {},\n      byOperator: {},\n      byConditions: {},\n      dailyTrend: {},\n      productivityTrend: []\n    };\n    logs.forEach(log => {\n      // Totali\n      stats.totalQuantity += log.quantity || 0;\n      stats.totalManHours += log.total_man_hours || 0;\n\n      // Per attività\n      if (!stats.byActivity[log.activity_type]) {\n        stats.byActivity[log.activity_type] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byActivity[log.activity_type].count++;\n      stats.byActivity[log.activity_type].quantity += log.quantity || 0;\n      stats.byActivity[log.activity_type].manHours += log.total_man_hours || 0;\n\n      // Per operatore\n      const operatorKey = `${log.operator_id}`;\n      if (!stats.byOperator[operatorKey]) {\n        stats.byOperator[operatorKey] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byOperator[operatorKey].count++;\n      stats.byOperator[operatorKey].quantity += log.quantity || 0;\n      stats.byOperator[operatorKey].manHours += log.total_man_hours || 0;\n\n      // Per condizioni ambientali\n      if (!stats.byConditions[log.environmental_conditions]) {\n        stats.byConditions[log.environmental_conditions] = {\n          count: 0,\n          quantity: 0,\n          productivity: 0\n        };\n      }\n      stats.byConditions[log.environmental_conditions].count++;\n      stats.byConditions[log.environmental_conditions].quantity += log.quantity || 0;\n\n      // Trend giornaliero\n      const date = new Date(log.start_timestamp).toISOString().split('T')[0];\n      if (!stats.dailyTrend[date]) {\n        stats.dailyTrend[date] = {\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.dailyTrend[date].quantity += log.quantity || 0;\n      stats.dailyTrend[date].manHours += log.total_man_hours || 0;\n    });\n\n    // Calcola produttività medie\n    stats.averageProductivity = stats.totalManHours > 0 ? stats.totalQuantity / stats.totalManHours : 0;\n    Object.keys(stats.byActivity).forEach(activity => {\n      const data = stats.byActivity[activity];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    Object.keys(stats.byOperator).forEach(operator => {\n      const data = stats.byOperator[operator];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    Object.keys(stats.byConditions).forEach(condition => {\n      const data = stats.byConditions[condition];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    Object.keys(stats.dailyTrend).forEach(date => {\n      const data = stats.dailyTrend[date];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n    setStatistics(stats);\n  };\n\n  // Configurazioni grafici\n  const productivityByActivityChart = {\n    labels: Object.keys(statistics.byActivity || {}),\n    datasets: [{\n      label: 'Produttività (unità/ora)',\n      data: Object.values(statistics.byActivity || {}).map(d => d.productivity.toFixed(2)),\n      backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n      borderColor: ['#2563EB', '#059669', '#D97706', '#DC2626'],\n      borderWidth: 1\n    }]\n  };\n  const dailyTrendChart = {\n    labels: Object.keys(statistics.dailyTrend || {}).sort(),\n    datasets: [{\n      label: 'Quantità Giornaliera',\n      data: Object.keys(statistics.dailyTrend || {}).sort().map(date => statistics.dailyTrend[date].quantity),\n      borderColor: '#3B82F6',\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      tension: 0.1\n    }, {\n      label: 'Produttività Giornaliera',\n      data: Object.keys(statistics.dailyTrend || {}).sort().map(date => statistics.dailyTrend[date].productivity),\n      borderColor: '#10B981',\n      backgroundColor: 'rgba(16, 185, 129, 0.1)',\n      tension: 0.1,\n      yAxisID: 'y1'\n    }]\n  };\n  const conditionsChart = {\n    labels: Object.keys(statistics.byConditions || {}),\n    datasets: [{\n      data: Object.values(statistics.byConditions || {}).map(d => d.count),\n      backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n      borderWidth: 2\n    }]\n  };\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top'\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true\n      },\n      y1: {\n        type: 'linear',\n        display: true,\n        position: 'right',\n        grid: {\n          drawOnChartArea: false\n        }\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ml-4 text-lg\",\n        children: \"Caricamento dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-800\",\n        children: \"\\uD83D\\uDCCA Dashboard Produttivit\\xE0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedCantiere,\n          onChange: e => setSelectedCantiere(e.target.value),\n          className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Tutti i cantieri\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: cantiere.id_cantiere,\n            children: cantiere.commessa\n          }, cantiere.id_cantiere, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"date\",\n          value: dateRange.start,\n          onChange: e => setDateRange(prev => ({\n            ...prev,\n            start: e.target.value\n          })),\n          className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"date\",\n          value: dateRange.end,\n          onChange: e => setDateRange(prev => ({\n            ...prev,\n            end: e.target.value\n          })),\n          className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mr-4\",\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Work Logs Totali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: statistics.totalLogs\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mr-4\",\n            children: \"\\uD83D\\uDCCF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Quantit\\xE0 Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: (_statistics$totalQuan = statistics.totalQuantity) === null || _statistics$totalQuan === void 0 ? void 0 : _statistics$totalQuan.toFixed(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mr-4\",\n            children: \"\\u23F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Ore-Uomo Totali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: (_statistics$totalManH = statistics.totalManHours) === null || _statistics$totalManH === void 0 ? void 0 : _statistics$totalManH.toFixed(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl mr-4\",\n            children: \"\\u26A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Produttivit\\xE0 Media\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: (_statistics$averagePr = statistics.averageProductivity) === null || _statistics$averagePr === void 0 ? void 0 : _statistics$averagePr.toFixed(2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"unit\\xE0/ora\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-800 mb-4\",\n          children: \"Produttivit\\xE0 per Attivit\\xE0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), Object.keys(statistics.byActivity || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Bar, {\n          data: productivityByActivityChart,\n          options: chartOptions\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-gray-500 py-8\",\n          children: \"Nessun dato disponibile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-800 mb-4\",\n          children: \"Condizioni Ambientali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), Object.keys(statistics.byConditions || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Doughnut, {\n          data: conditionsChart\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-gray-500 py-8\",\n          children: \"Nessun dato disponibile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-6 rounded-lg shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-800 mb-4\",\n        children: \"Trend Giornaliero\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), Object.keys(statistics.dailyTrend || {}).length > 0 ? /*#__PURE__*/_jsxDEV(Line, {\n        data: dailyTrendChart,\n        options: chartOptions\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-gray-500 py-8\",\n        children: \"Nessun dato disponibile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-6 rounded-lg shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-800 mb-4\",\n        children: \"Dettagli per Attivit\\xE0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Attivit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Work Logs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Quantit\\xE0 Totale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Ore-Uomo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Produttivit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: Object.entries(statistics.byActivity || {}).map(([activity, data]) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                children: activity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: data.count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: data.quantity.toFixed(1)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: data.manHours.toFixed(1)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: [data.productivity.toFixed(2), \" unit\\xE0/ora\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this)]\n            }, activity, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 268,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"wA34wQBYuMVKTEKEJCMksNeQ7T4=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Line", "Bar", "Doughnut", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "axiosInstance", "jsxDEV", "_jsxDEV", "register", "Dashboard", "_s", "_statistics$totalQuan", "_statistics$totalManH", "_statistics$averagePr", "workLogs", "setWorkLogs", "statistics", "setStatistics", "loading", "setLoading", "selected<PERSON><PERSON><PERSON>", "setSelectedCantiere", "cantieri", "set<PERSON><PERSON><PERSON>", "date<PERSON><PERSON><PERSON>", "setDateRange", "start", "Date", "now", "toISOString", "split", "end", "loadInitialData", "length", "loadDashboardData", "cantieriRes", "get", "data", "id_cantiere", "toString", "error", "console", "params", "start_date", "end_date", "per_page", "workLogsRes", "work_logs", "calculateStatistics", "logs", "stats", "totalLogs", "totalQuantity", "totalManHours", "averageProductivity", "byActivity", "byOperator", "byConditions", "dailyTrend", "productivityTrend", "for<PERSON>ach", "log", "quantity", "total_man_hours", "activity_type", "count", "manHours", "productivity", "operatorKey", "operator_id", "environmental_conditions", "date", "start_timestamp", "Object", "keys", "activity", "operator", "condition", "productivityByActivityChart", "labels", "datasets", "label", "values", "map", "d", "toFixed", "backgroundColor", "borderColor", "borderWidth", "dailyTrendChart", "sort", "tension", "yAxisID", "<PERSON><PERSON><PERSON>", "chartOptions", "responsive", "plugins", "legend", "position", "scales", "y", "beginAtZero", "y1", "type", "display", "grid", "drawOnChartArea", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "e", "target", "cantiere", "commessa", "prev", "options", "entries", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Line, Bar, Doughnut } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n} from 'chart.js';\nimport axiosInstance from '../../services/axiosConfig';\n\n// Registra i componenti Chart.js\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement\n);\n\nconst Dashboard = () => {\n  const [workLogs, setWorkLogs] = useState([]);\n  const [statistics, setStatistics] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [selectedCantiere, setSelectedCantiere] = useState('');\n  const [cantieri, setCantieri] = useState([]);\n  const [dateRange, setDateRange] = useState({\n    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 giorni fa\n    end: new Date().toISOString().split('T')[0] // oggi\n  });\n\n  useEffect(() => {\n    loadInitialData();\n  }, []);\n\n  useEffect(() => {\n    if (cantieri.length > 0) {\n      loadDashboardData();\n    }\n  }, [selectedCantiere, dateRange, cantieri]);\n\n  const loadInitialData = async () => {\n    try {\n      const cantieriRes = await axiosInstance.get('/cantieri');\n      setCantieri(cantieriRes.data || []);\n      \n      // Seleziona il primo cantiere automaticamente\n      if (cantieriRes.data && cantieriRes.data.length > 0) {\n        setSelectedCantiere(cantieriRes.data[0].id_cantiere.toString());\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento cantieri:', error);\n    }\n  };\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      // Parametri per le chiamate API\n      const params = {\n        id_cantiere: selectedCantiere,\n        start_date: dateRange.start,\n        end_date: dateRange.end,\n        per_page: 100\n      };\n\n      // Carica work logs\n      const workLogsRes = await axiosInstance.get('/v1/work-logs', { params });\n      setWorkLogs(workLogsRes.data.work_logs || []);\n\n      // Calcola statistiche\n      calculateStatistics(workLogsRes.data.work_logs || []);\n\n    } catch (error) {\n      console.error('Errore nel caricamento dati dashboard:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateStatistics = (logs) => {\n    const stats = {\n      totalLogs: logs.length,\n      totalQuantity: 0,\n      totalManHours: 0,\n      averageProductivity: 0,\n      byActivity: {},\n      byOperator: {},\n      byConditions: {},\n      dailyTrend: {},\n      productivityTrend: []\n    };\n\n    logs.forEach(log => {\n      // Totali\n      stats.totalQuantity += log.quantity || 0;\n      stats.totalManHours += log.total_man_hours || 0;\n\n      // Per attività\n      if (!stats.byActivity[log.activity_type]) {\n        stats.byActivity[log.activity_type] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byActivity[log.activity_type].count++;\n      stats.byActivity[log.activity_type].quantity += log.quantity || 0;\n      stats.byActivity[log.activity_type].manHours += log.total_man_hours || 0;\n\n      // Per operatore\n      const operatorKey = `${log.operator_id}`;\n      if (!stats.byOperator[operatorKey]) {\n        stats.byOperator[operatorKey] = {\n          count: 0,\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.byOperator[operatorKey].count++;\n      stats.byOperator[operatorKey].quantity += log.quantity || 0;\n      stats.byOperator[operatorKey].manHours += log.total_man_hours || 0;\n\n      // Per condizioni ambientali\n      if (!stats.byConditions[log.environmental_conditions]) {\n        stats.byConditions[log.environmental_conditions] = {\n          count: 0,\n          quantity: 0,\n          productivity: 0\n        };\n      }\n      stats.byConditions[log.environmental_conditions].count++;\n      stats.byConditions[log.environmental_conditions].quantity += log.quantity || 0;\n\n      // Trend giornaliero\n      const date = new Date(log.start_timestamp).toISOString().split('T')[0];\n      if (!stats.dailyTrend[date]) {\n        stats.dailyTrend[date] = {\n          quantity: 0,\n          manHours: 0,\n          productivity: 0\n        };\n      }\n      stats.dailyTrend[date].quantity += log.quantity || 0;\n      stats.dailyTrend[date].manHours += log.total_man_hours || 0;\n    });\n\n    // Calcola produttività medie\n    stats.averageProductivity = stats.totalManHours > 0 ? stats.totalQuantity / stats.totalManHours : 0;\n\n    Object.keys(stats.byActivity).forEach(activity => {\n      const data = stats.byActivity[activity];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    Object.keys(stats.byOperator).forEach(operator => {\n      const data = stats.byOperator[operator];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    Object.keys(stats.byConditions).forEach(condition => {\n      const data = stats.byConditions[condition];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    Object.keys(stats.dailyTrend).forEach(date => {\n      const data = stats.dailyTrend[date];\n      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;\n    });\n\n    setStatistics(stats);\n  };\n\n  // Configurazioni grafici\n  const productivityByActivityChart = {\n    labels: Object.keys(statistics.byActivity || {}),\n    datasets: [\n      {\n        label: 'Produttività (unità/ora)',\n        data: Object.values(statistics.byActivity || {}).map(d => d.productivity.toFixed(2)),\n        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n        borderColor: ['#2563EB', '#059669', '#D97706', '#DC2626'],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const dailyTrendChart = {\n    labels: Object.keys(statistics.dailyTrend || {}).sort(),\n    datasets: [\n      {\n        label: 'Quantità Giornaliera',\n        data: Object.keys(statistics.dailyTrend || {}).sort().map(date => \n          statistics.dailyTrend[date].quantity\n        ),\n        borderColor: '#3B82F6',\n        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n        tension: 0.1,\n      },\n      {\n        label: 'Produttività Giornaliera',\n        data: Object.keys(statistics.dailyTrend || {}).sort().map(date => \n          statistics.dailyTrend[date].productivity\n        ),\n        borderColor: '#10B981',\n        backgroundColor: 'rgba(16, 185, 129, 0.1)',\n        tension: 0.1,\n        yAxisID: 'y1',\n      },\n    ],\n  };\n\n  const conditionsChart = {\n    labels: Object.keys(statistics.byConditions || {}),\n    datasets: [\n      {\n        data: Object.values(statistics.byConditions || {}).map(d => d.count),\n        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],\n        borderWidth: 2,\n      },\n    ],\n  };\n\n  const chartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'top',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n      },\n      y1: {\n        type: 'linear',\n        display: true,\n        position: 'right',\n        grid: {\n          drawOnChartArea: false,\n        },\n      },\n    },\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center p-8\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n        <span className=\"ml-4 text-lg\">Caricamento dashboard...</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-3xl font-bold text-gray-800\">📊 Dashboard Produttività</h1>\n        \n        {/* Filtri */}\n        <div className=\"flex space-x-4\">\n          <select\n            value={selectedCantiere}\n            onChange={(e) => setSelectedCantiere(e.target.value)}\n            className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"\">Tutti i cantieri</option>\n            {cantieri.map(cantiere => (\n              <option key={cantiere.id_cantiere} value={cantiere.id_cantiere}>\n                {cantiere.commessa}\n              </option>\n            ))}\n          </select>\n          \n          <input\n            type=\"date\"\n            value={dateRange.start}\n            onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}\n            className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          />\n          \n          <input\n            type=\"date\"\n            value={dateRange.end}\n            onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}\n            className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          />\n        </div>\n      </div>\n\n      {/* KPI Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n          <div className=\"flex items-center\">\n            <div className=\"text-3xl mr-4\">📝</div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Work Logs Totali</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{statistics.totalLogs}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n          <div className=\"flex items-center\">\n            <div className=\"text-3xl mr-4\">📏</div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Quantità Totale</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{statistics.totalQuantity?.toFixed(1)}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n          <div className=\"flex items-center\">\n            <div className=\"text-3xl mr-4\">⏰</div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Ore-Uomo Totali</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{statistics.totalManHours?.toFixed(1)}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n          <div className=\"flex items-center\">\n            <div className=\"text-3xl mr-4\">⚡</div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Produttività Media</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{statistics.averageProductivity?.toFixed(2)}</p>\n              <p className=\"text-xs text-gray-500\">unità/ora</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Grafici */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Produttività per Attività */}\n        <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Produttività per Attività</h3>\n          {Object.keys(statistics.byActivity || {}).length > 0 ? (\n            <Bar data={productivityByActivityChart} options={chartOptions} />\n          ) : (\n            <div className=\"text-center text-gray-500 py-8\">Nessun dato disponibile</div>\n          )}\n        </div>\n\n        {/* Distribuzione Condizioni Ambientali */}\n        <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Condizioni Ambientali</h3>\n          {Object.keys(statistics.byConditions || {}).length > 0 ? (\n            <Doughnut data={conditionsChart} />\n          ) : (\n            <div className=\"text-center text-gray-500 py-8\">Nessun dato disponibile</div>\n          )}\n        </div>\n      </div>\n\n      {/* Trend Giornaliero */}\n      <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Trend Giornaliero</h3>\n        {Object.keys(statistics.dailyTrend || {}).length > 0 ? (\n          <Line data={dailyTrendChart} options={chartOptions} />\n        ) : (\n          <div className=\"text-center text-gray-500 py-8\">Nessun dato disponibile</div>\n        )}\n      </div>\n\n      {/* Tabella Dettagli per Attività */}\n      <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n        <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Dettagli per Attività</h3>\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Attività\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Work Logs\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Quantità Totale\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Ore-Uomo\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Produttività\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {Object.entries(statistics.byActivity || {}).map(([activity, data]) => (\n                <tr key={activity}>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                    {activity}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {data.count}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {data.quantity.toFixed(1)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {data.manHours.toFixed(1)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {data.productivity.toFixed(2)} unità/ora\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AACrD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,UAAU;AACjB,OAAOC,aAAa,MAAM,4BAA4B;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAZ,OAAO,CAACa,QAAQ,CACdZ,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UACF,CAAC;AAED,MAAMK,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC;IACzCqC,KAAK,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAE;IACpFC,GAAG,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC;EAEFxC,SAAS,CAAC,MAAM;IACd0C,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN1C,SAAS,CAAC,MAAM;IACd,IAAIgC,QAAQ,CAACW,MAAM,GAAG,CAAC,EAAE;MACvBC,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACd,gBAAgB,EAAEI,SAAS,EAAEF,QAAQ,CAAC,CAAC;EAE3C,MAAMU,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMG,WAAW,GAAG,MAAM9B,aAAa,CAAC+B,GAAG,CAAC,WAAW,CAAC;MACxDb,WAAW,CAACY,WAAW,CAACE,IAAI,IAAI,EAAE,CAAC;;MAEnC;MACA,IAAIF,WAAW,CAACE,IAAI,IAAIF,WAAW,CAACE,IAAI,CAACJ,MAAM,GAAG,CAAC,EAAE;QACnDZ,mBAAmB,CAACc,WAAW,CAACE,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;EAED,MAAMN,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMuB,MAAM,GAAG;QACbJ,WAAW,EAAElB,gBAAgB;QAC7BuB,UAAU,EAAEnB,SAAS,CAACE,KAAK;QAC3BkB,QAAQ,EAAEpB,SAAS,CAACO,GAAG;QACvBc,QAAQ,EAAE;MACZ,CAAC;;MAED;MACA,MAAMC,WAAW,GAAG,MAAMzC,aAAa,CAAC+B,GAAG,CAAC,eAAe,EAAE;QAAEM;MAAO,CAAC,CAAC;MACxE3B,WAAW,CAAC+B,WAAW,CAACT,IAAI,CAACU,SAAS,IAAI,EAAE,CAAC;;MAE7C;MACAC,mBAAmB,CAACF,WAAW,CAACT,IAAI,CAACU,SAAS,IAAI,EAAE,CAAC;IAEvD,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,KAAK,GAAG;MACZC,SAAS,EAAEF,IAAI,CAAChB,MAAM;MACtBmB,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,mBAAmB,EAAE,CAAC;MACtBC,UAAU,EAAE,CAAC,CAAC;MACdC,UAAU,EAAE,CAAC,CAAC;MACdC,YAAY,EAAE,CAAC,CAAC;MAChBC,UAAU,EAAE,CAAC,CAAC;MACdC,iBAAiB,EAAE;IACrB,CAAC;IAEDV,IAAI,CAACW,OAAO,CAACC,GAAG,IAAI;MAClB;MACAX,KAAK,CAACE,aAAa,IAAIS,GAAG,CAACC,QAAQ,IAAI,CAAC;MACxCZ,KAAK,CAACG,aAAa,IAAIQ,GAAG,CAACE,eAAe,IAAI,CAAC;;MAE/C;MACA,IAAI,CAACb,KAAK,CAACK,UAAU,CAACM,GAAG,CAACG,aAAa,CAAC,EAAE;QACxCd,KAAK,CAACK,UAAU,CAACM,GAAG,CAACG,aAAa,CAAC,GAAG;UACpCC,KAAK,EAAE,CAAC;UACRH,QAAQ,EAAE,CAAC;UACXI,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACK,UAAU,CAACM,GAAG,CAACG,aAAa,CAAC,CAACC,KAAK,EAAE;MAC3Cf,KAAK,CAACK,UAAU,CAACM,GAAG,CAACG,aAAa,CAAC,CAACF,QAAQ,IAAID,GAAG,CAACC,QAAQ,IAAI,CAAC;MACjEZ,KAAK,CAACK,UAAU,CAACM,GAAG,CAACG,aAAa,CAAC,CAACE,QAAQ,IAAIL,GAAG,CAACE,eAAe,IAAI,CAAC;;MAExE;MACA,MAAMK,WAAW,GAAG,GAAGP,GAAG,CAACQ,WAAW,EAAE;MACxC,IAAI,CAACnB,KAAK,CAACM,UAAU,CAACY,WAAW,CAAC,EAAE;QAClClB,KAAK,CAACM,UAAU,CAACY,WAAW,CAAC,GAAG;UAC9BH,KAAK,EAAE,CAAC;UACRH,QAAQ,EAAE,CAAC;UACXI,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACM,UAAU,CAACY,WAAW,CAAC,CAACH,KAAK,EAAE;MACrCf,KAAK,CAACM,UAAU,CAACY,WAAW,CAAC,CAACN,QAAQ,IAAID,GAAG,CAACC,QAAQ,IAAI,CAAC;MAC3DZ,KAAK,CAACM,UAAU,CAACY,WAAW,CAAC,CAACF,QAAQ,IAAIL,GAAG,CAACE,eAAe,IAAI,CAAC;;MAElE;MACA,IAAI,CAACb,KAAK,CAACO,YAAY,CAACI,GAAG,CAACS,wBAAwB,CAAC,EAAE;QACrDpB,KAAK,CAACO,YAAY,CAACI,GAAG,CAACS,wBAAwB,CAAC,GAAG;UACjDL,KAAK,EAAE,CAAC;UACRH,QAAQ,EAAE,CAAC;UACXK,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACO,YAAY,CAACI,GAAG,CAACS,wBAAwB,CAAC,CAACL,KAAK,EAAE;MACxDf,KAAK,CAACO,YAAY,CAACI,GAAG,CAACS,wBAAwB,CAAC,CAACR,QAAQ,IAAID,GAAG,CAACC,QAAQ,IAAI,CAAC;;MAE9E;MACA,MAAMS,IAAI,GAAG,IAAI5C,IAAI,CAACkC,GAAG,CAACW,eAAe,CAAC,CAAC3C,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtE,IAAI,CAACoB,KAAK,CAACQ,UAAU,CAACa,IAAI,CAAC,EAAE;QAC3BrB,KAAK,CAACQ,UAAU,CAACa,IAAI,CAAC,GAAG;UACvBT,QAAQ,EAAE,CAAC;UACXI,QAAQ,EAAE,CAAC;UACXC,YAAY,EAAE;QAChB,CAAC;MACH;MACAjB,KAAK,CAACQ,UAAU,CAACa,IAAI,CAAC,CAACT,QAAQ,IAAID,GAAG,CAACC,QAAQ,IAAI,CAAC;MACpDZ,KAAK,CAACQ,UAAU,CAACa,IAAI,CAAC,CAACL,QAAQ,IAAIL,GAAG,CAACE,eAAe,IAAI,CAAC;IAC7D,CAAC,CAAC;;IAEF;IACAb,KAAK,CAACI,mBAAmB,GAAGJ,KAAK,CAACG,aAAa,GAAG,CAAC,GAAGH,KAAK,CAACE,aAAa,GAAGF,KAAK,CAACG,aAAa,GAAG,CAAC;IAEnGoB,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACK,UAAU,CAAC,CAACK,OAAO,CAACe,QAAQ,IAAI;MAChD,MAAMtC,IAAI,GAAGa,KAAK,CAACK,UAAU,CAACoB,QAAQ,CAAC;MACvCtC,IAAI,CAAC8B,YAAY,GAAG9B,IAAI,CAAC6B,QAAQ,GAAG,CAAC,GAAG7B,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAAC6B,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFO,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACM,UAAU,CAAC,CAACI,OAAO,CAACgB,QAAQ,IAAI;MAChD,MAAMvC,IAAI,GAAGa,KAAK,CAACM,UAAU,CAACoB,QAAQ,CAAC;MACvCvC,IAAI,CAAC8B,YAAY,GAAG9B,IAAI,CAAC6B,QAAQ,GAAG,CAAC,GAAG7B,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAAC6B,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFO,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACO,YAAY,CAAC,CAACG,OAAO,CAACiB,SAAS,IAAI;MACnD,MAAMxC,IAAI,GAAGa,KAAK,CAACO,YAAY,CAACoB,SAAS,CAAC;MAC1CxC,IAAI,CAAC8B,YAAY,GAAG9B,IAAI,CAAC6B,QAAQ,GAAG,CAAC,GAAG7B,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAAC6B,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFO,MAAM,CAACC,IAAI,CAACxB,KAAK,CAACQ,UAAU,CAAC,CAACE,OAAO,CAACW,IAAI,IAAI;MAC5C,MAAMlC,IAAI,GAAGa,KAAK,CAACQ,UAAU,CAACa,IAAI,CAAC;MACnClC,IAAI,CAAC8B,YAAY,GAAG9B,IAAI,CAAC6B,QAAQ,GAAG,CAAC,GAAG7B,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAAC6B,QAAQ,GAAG,CAAC;IAC3E,CAAC,CAAC;IAEFjD,aAAa,CAACiC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAM4B,2BAA2B,GAAG;IAClCC,MAAM,EAAEN,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACuC,UAAU,IAAI,CAAC,CAAC,CAAC;IAChDyB,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,0BAA0B;MACjC5C,IAAI,EAAEoC,MAAM,CAACS,MAAM,CAAClE,UAAU,CAACuC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC4B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,YAAY,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC;MACpFC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAC7DC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACzDC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBV,MAAM,EAAEN,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC0C,UAAU,IAAI,CAAC,CAAC,CAAC,CAACgC,IAAI,CAAC,CAAC;IACvDV,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,sBAAsB;MAC7B5C,IAAI,EAAEoC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC0C,UAAU,IAAI,CAAC,CAAC,CAAC,CAACgC,IAAI,CAAC,CAAC,CAACP,GAAG,CAACZ,IAAI,IAC5DvD,UAAU,CAAC0C,UAAU,CAACa,IAAI,CAAC,CAACT,QAC9B,CAAC;MACDyB,WAAW,EAAE,SAAS;MACtBD,eAAe,EAAE,yBAAyB;MAC1CK,OAAO,EAAE;IACX,CAAC,EACD;MACEV,KAAK,EAAE,0BAA0B;MACjC5C,IAAI,EAAEoC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC0C,UAAU,IAAI,CAAC,CAAC,CAAC,CAACgC,IAAI,CAAC,CAAC,CAACP,GAAG,CAACZ,IAAI,IAC5DvD,UAAU,CAAC0C,UAAU,CAACa,IAAI,CAAC,CAACJ,YAC9B,CAAC;MACDoB,WAAW,EAAE,SAAS;MACtBD,eAAe,EAAE,yBAAyB;MAC1CK,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE;IACX,CAAC;EAEL,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBd,MAAM,EAAEN,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACyC,YAAY,IAAI,CAAC,CAAC,CAAC;IAClDuB,QAAQ,EAAE,CACR;MACE3C,IAAI,EAAEoC,MAAM,CAACS,MAAM,CAAClE,UAAU,CAACyC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC0B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,KAAK,CAAC;MACpEqB,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAC7DE,WAAW,EAAE;IACf,CAAC;EAEL,CAAC;EAED,MAAMM,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE;MACf,CAAC;MACDC,EAAE,EAAE;QACFC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,IAAI;QACbN,QAAQ,EAAE,OAAO;QACjBO,IAAI,EAAE;UACJC,eAAe,EAAE;QACnB;MACF;IACF;EACF,CAAC;EAED,IAAIxF,OAAO,EAAE;IACX,oBACEX,OAAA;MAAKoG,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDrG,OAAA;QAAKoG,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtFzG,OAAA;QAAMoG,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC;EAEV;EAEA,oBACEzG,OAAA;IAAKoG,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BrG,OAAA;MAAKoG,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrG,OAAA;QAAIoG,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAG/EzG,OAAA;QAAKoG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrG,OAAA;UACE0G,KAAK,EAAE7F,gBAAiB;UACxB8F,QAAQ,EAAGC,CAAC,IAAK9F,mBAAmB,CAAC8F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACrDN,SAAS,EAAC,iGAAiG;UAAAC,QAAA,gBAE3GrG,OAAA;YAAQ0G,KAAK,EAAC,EAAE;YAAAL,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACzC1F,QAAQ,CAAC6D,GAAG,CAACkC,QAAQ,iBACpB9G,OAAA;YAAmC0G,KAAK,EAAEI,QAAQ,CAAC/E,WAAY;YAAAsE,QAAA,EAC5DS,QAAQ,CAACC;UAAQ,GADPD,QAAQ,CAAC/E,WAAW;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEzB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAETzG,OAAA;UACEgG,IAAI,EAAC,MAAM;UACXU,KAAK,EAAEzF,SAAS,CAACE,KAAM;UACvBwF,QAAQ,EAAGC,CAAC,IAAK1F,YAAY,CAAC8F,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE7F,KAAK,EAAEyF,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAC,CAAE;UAC5EN,SAAS,EAAC;QAAiG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC,eAEFzG,OAAA;UACEgG,IAAI,EAAC,MAAM;UACXU,KAAK,EAAEzF,SAAS,CAACO,GAAI;UACrBmF,QAAQ,EAAGC,CAAC,IAAK1F,YAAY,CAAC8F,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAExF,GAAG,EAAEoF,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAC,CAAE;UAC1EN,SAAS,EAAC;QAAiG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzG,OAAA;MAAKoG,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDrG,OAAA;QAAKoG,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDrG,OAAA;UAAKoG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCrG,OAAA;YAAKoG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCzG,OAAA;YAAAqG,QAAA,gBACErG,OAAA;cAAGoG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrEzG,OAAA;cAAGoG,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAE5F,UAAU,CAACmC;YAAS;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzG,OAAA;QAAKoG,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDrG,OAAA;UAAKoG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCrG,OAAA;YAAKoG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCzG,OAAA;YAAAqG,QAAA,gBACErG,OAAA;cAAGoG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEzG,OAAA;cAAGoG,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAAjG,qBAAA,GAAEK,UAAU,CAACoC,aAAa,cAAAzC,qBAAA,uBAAxBA,qBAAA,CAA0B0E,OAAO,CAAC,CAAC;YAAC;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzG,OAAA;QAAKoG,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDrG,OAAA;UAAKoG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCrG,OAAA;YAAKoG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCzG,OAAA;YAAAqG,QAAA,gBACErG,OAAA;cAAGoG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEzG,OAAA;cAAGoG,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAAhG,qBAAA,GAAEI,UAAU,CAACqC,aAAa,cAAAzC,qBAAA,uBAAxBA,qBAAA,CAA0ByE,OAAO,CAAC,CAAC;YAAC;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzG,OAAA;QAAKoG,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDrG,OAAA;UAAKoG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCrG,OAAA;YAAKoG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCzG,OAAA;YAAAqG,QAAA,gBACErG,OAAA;cAAGoG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvEzG,OAAA;cAAGoG,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAA/F,qBAAA,GAAEG,UAAU,CAACsC,mBAAmB,cAAAzC,qBAAA,uBAA9BA,qBAAA,CAAgCwE,OAAO,CAAC,CAAC;YAAC;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChGzG,OAAA;cAAGoG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzG,OAAA;MAAKoG,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDrG,OAAA;QAAKoG,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDrG,OAAA;UAAIoG,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACtFvC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACuC,UAAU,IAAI,CAAC,CAAC,CAAC,CAACtB,MAAM,GAAG,CAAC,gBAClD1B,OAAA,CAACf,GAAG;UAAC6C,IAAI,EAAEyC,2BAA4B;UAAC0C,OAAO,EAAE1B;QAAa;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEjEzG,OAAA;UAAKoG,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAC7E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzG,OAAA;QAAKoG,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDrG,OAAA;UAAIoG,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAClFvC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAACyC,YAAY,IAAI,CAAC,CAAC,CAAC,CAACxB,MAAM,GAAG,CAAC,gBACpD1B,OAAA,CAACd,QAAQ;UAAC4C,IAAI,EAAEwD;QAAgB;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEnCzG,OAAA;UAAKoG,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAC7E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzG,OAAA;MAAKoG,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrG,OAAA;QAAIoG,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC9EvC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC0C,UAAU,IAAI,CAAC,CAAC,CAAC,CAACzB,MAAM,GAAG,CAAC,gBAClD1B,OAAA,CAAChB,IAAI;QAAC8C,IAAI,EAAEoD,eAAgB;QAAC+B,OAAO,EAAE1B;MAAa;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEtDzG,OAAA;QAAKoG,SAAS,EAAC,gCAAgC;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAC7E;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNzG,OAAA;MAAKoG,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrG,OAAA;QAAIoG,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnFzG,OAAA;QAAKoG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BrG,OAAA;UAAOoG,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDrG,OAAA;YAAOoG,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BrG,OAAA;cAAAqG,QAAA,gBACErG,OAAA;gBAAIoG,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRzG,OAAA;YAAOoG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDnC,MAAM,CAACgD,OAAO,CAACzG,UAAU,CAACuC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC4B,GAAG,CAAC,CAAC,CAACR,QAAQ,EAAEtC,IAAI,CAAC,kBAChE9B,OAAA;cAAAqG,QAAA,gBACErG,OAAA;gBAAIoG,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1EjC;cAAQ;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DvE,IAAI,CAAC4B;cAAK;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DvE,IAAI,CAACyB,QAAQ,CAACuB,OAAO,CAAC,CAAC;cAAC;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DvE,IAAI,CAAC6B,QAAQ,CAACmB,OAAO,CAAC,CAAC;cAAC;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACLzG,OAAA;gBAAIoG,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAC9DvE,IAAI,CAAC8B,YAAY,CAACkB,OAAO,CAAC,CAAC,CAAC,EAAC,eAChC;cAAA;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAfErC,QAAQ;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBb,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtG,EAAA,CAhZID,SAAS;AAAAiH,EAAA,GAATjH,SAAS;AAkZf,eAAeA,SAAS;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}