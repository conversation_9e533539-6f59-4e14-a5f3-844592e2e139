#!/usr/bin/env python3
"""
Script per testare il sistema completo di produttività.
Verifica backend, frontend e integrazione.
"""

import requests
import time
import subprocess
import sys
from datetime import datetime

def test_backend_health():
    """Testa la salute del backend."""
    try:
        print("🔍 Test Backend Health...")
        response = requests.get("http://localhost:8001/api/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend OK: {data.get('status')}")
            print(f"   Database: {data.get('database')}")
            return True
        else:
            print(f"❌ Backend Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Backend non raggiungibile: {str(e)}")
        return False

def test_productivity_apis():
    """Testa le API di produttività."""
    try:
        print("\n🧪 Test API Produttività...")
        
        # Login
        login_data = {"username": "admin", "password": "admin"}
        response = requests.post("http://localhost:8001/api/auth/login", data=login_data, timeout=10)
        
        if response.status_code != 200:
            print("❌ Login fallito")
            return False
            
        token = response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test work logs endpoint
        response = requests.get("http://localhost:8001/api/v1/work-logs?per_page=5", headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Work Logs API OK: {data.get('total_count', 0)} logs trovati")
        else:
            print(f"❌ Work Logs API Error: {response.status_code}")
            return False
        
        # Test estimation endpoint
        estimation_data = {
            "activity_type": "Posa",
            "quantity_required": 100.0,
            "environmental_conditions": "Normale",
            "tools_used": "Manuale",
            "number_of_operators": 2
        }
        
        response = requests.post("http://localhost:8001/api/v1/predict/estimation", 
                               headers=headers, json=estimation_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Estimation API OK: {data.get('estimated_time_for_team_hours', 0):.2f} ore stimate")
        else:
            print(f"❌ Estimation API Error: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test API: {str(e)}")
        return False

def test_frontend_availability():
    """Testa la disponibilità del frontend."""
    try:
        print("\n🌐 Test Frontend Availability...")
        response = requests.get("http://localhost:3000", timeout=10)
        
        if response.status_code == 200:
            print("✅ Frontend raggiungibile")
            return True
        else:
            print(f"❌ Frontend Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Frontend non raggiungibile: {str(e)}")
        print("   Assicurati che il frontend sia avviato con 'npm start'")
        return False

def check_database_tables():
    """Verifica che le tabelle del database siano state create."""
    try:
        print("\n🗄️ Test Database Tables...")
        
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))
        
        from modules.database_pg import database_connection
        
        with database_connection() as (conn, cursor):
            # Verifica tabella work_logs
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_name = 'work_logs'
            """)
            work_logs_exists = cursor.fetchone()[0] > 0
            
            # Verifica campi produttività in tipologie_cavi
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.columns 
                WHERE table_name = 'tipologie_cavi' 
                AND column_name LIKE '%productivity%'
            """)
            productivity_fields = cursor.fetchone()[0]
            
            # Verifica campo experience_level in responsabili
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.columns 
                WHERE table_name = 'responsabili' 
                AND column_name = 'experience_level'
            """)
            experience_field = cursor.fetchone()[0] > 0
            
            if work_logs_exists:
                print("✅ Tabella work_logs presente")
            else:
                print("❌ Tabella work_logs mancante")
                
            if productivity_fields >= 2:
                print(f"✅ Campi produttività presenti ({productivity_fields})")
            else:
                print(f"❌ Campi produttività mancanti ({productivity_fields})")
                
            if experience_field:
                print("✅ Campo experience_level presente")
            else:
                print("❌ Campo experience_level mancante")
            
            return work_logs_exists and productivity_fields >= 2 and experience_field
        
    except Exception as e:
        print(f"❌ Errore verifica database: {str(e)}")
        return False

def generate_system_report():
    """Genera un report dello stato del sistema."""
    print("\n📊 REPORT SISTEMA PRODUTTIVITÀ")
    print("=" * 60)
    print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test componenti
    backend_ok = test_backend_health()
    database_ok = check_database_tables()
    apis_ok = test_productivity_apis()
    frontend_ok = test_frontend_availability()
    
    print(f"\n📋 STATO COMPONENTI:")
    print(f"   Backend:     {'✅ OK' if backend_ok else '❌ ERRORE'}")
    print(f"   Database:    {'✅ OK' if database_ok else '❌ ERRORE'}")
    print(f"   API:         {'✅ OK' if apis_ok else '❌ ERRORE'}")
    print(f"   Frontend:    {'✅ OK' if frontend_ok else '❌ ERRORE'}")
    
    # Calcola stato generale
    all_ok = backend_ok and database_ok and apis_ok and frontend_ok
    
    print(f"\n🎯 STATO GENERALE: {'✅ SISTEMA FUNZIONANTE' if all_ok else '❌ PROBLEMI RILEVATI'}")
    
    if all_ok:
        print("\n🎉 CONGRATULAZIONI!")
        print("Il sistema di produttività è completamente funzionante!")
        print("\n📚 FUNZIONALITÀ DISPONIBILI:")
        print("- ✅ Creazione e gestione work logs")
        print("- ✅ Calcolo produttività storica")
        print("- ✅ Stima predittiva tempi di lavoro")
        print("- ✅ Dashboard con grafici e KPI")
        print("- ✅ Interfaccia web completa")
        
        print("\n🔗 ACCESSO AL SISTEMA:")
        print("- Backend API: http://localhost:8001/docs")
        print("- Frontend Web: http://localhost:3000/dashboard/produttivita")
        print("- Login: admin / admin")
        
        print("\n📖 IMPLEMENTAZIONE COMPLETATA:")
        print("- ✅ Task 1: Database Schema Design")
        print("- ✅ Task 2: Backend API Endpoints")
        print("- ✅ Task 3: Frontend Interface (React.js)")
        print("- 🔄 Task 4: Machine Learning Module (Opzionale)")
        
    else:
        print("\n🔧 AZIONI RICHIESTE:")
        if not backend_ok:
            print("- Avviare il backend: python webapp/run_system_simple.py")
        if not database_ok:
            print("- Aggiornare il database: python update_database_productivity.py")
        if not frontend_ok:
            print("- Avviare il frontend: cd webapp/frontend && npm start")
        if not apis_ok:
            print("- Verificare configurazione API e riavviare backend")
    
    return all_ok

def main():
    """Funzione principale."""
    print("🚀 TEST SISTEMA COMPLETO PRODUTTIVITÀ")
    print("=" * 60)
    
    success = generate_system_report()
    
    if success:
        print("\n✅ SISTEMA PRONTO PER L'USO!")
        print("Puoi ora accedere al sistema di produttività tramite:")
        print("http://localhost:3000/dashboard/produttivita")
    else:
        print("\n❌ SISTEMA NON COMPLETAMENTE FUNZIONANTE")
        print("Segui le azioni richieste sopra per risolvere i problemi")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
