import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Paper,
  Chip,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  <PERSON>per,
  <PERSON>,
  StepLabel,
  StepContent
} from '@mui/material';
import {
  Calculate,
  TrendingUp,
  Schedule,
  Engineering,
  Speed,
  Assessment,
  CheckCircle,
  Info,
  Warning,
  Construction
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import axiosInstance from '../../services/axiosConfig';

const EstimationTool = () => {
  const { selectedCantiere: authSelectedCantiere } = useAuth();

  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage
  const currentCantiereId = authSelectedCantiere?.id_cantiere ||
                           parseInt(localStorage.getItem('selectedCantiereId'), 10) ||
                           null;
  const currentCantiereName = authSelectedCantiere?.commessa ||
                             localStorage.getItem('selectedCantiereName') ||
                             'Cantiere non selezionato';

  const [formData, setFormData] = useState({
    cable_type_id: '',
    activity_type: 'Posa',
    quantity_required: '',
    environmental_conditions: 'Normale',
    tools_used: 'Manuale',
    number_of_operators: 1,
    experience_level: 'Senior'
  });

  const [cableTypes, setCableTypes] = useState([]);
  const [estimation, setEstimation] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Opzioni per i dropdown
  const activityTypes = [
    { value: 'Posa', label: 'Posa' },
    { value: 'Collegamento', label: 'Collegamento' },
    { value: 'Certificazione', label: 'Certificazione' }
  ];

  const environmentalConditions = [
    { value: 'Normale', label: 'Normale' },
    { value: 'Spazi Ristretti', label: 'Spazi Ristretti' },
    { value: 'In Altezza', label: 'In Altezza' },
    { value: 'Esterno', label: 'Esterno' }
  ];

  const toolsUsed = [
    { value: 'Manuale', label: 'Manuale' },
    { value: 'Automatico', label: 'Automatico' }
  ];

  const experienceLevels = [
    { value: 'Apprentice', label: 'Apprendista' },
    { value: 'Junior', label: 'Junior' },
    { value: 'Senior', label: 'Senior' }
  ];

  useEffect(() => {
    loadCableTypes();
  }, []);

  const loadCableTypes = async () => {
    try {
      const response = await axiosInstance.get('/admin/tipologie-cavi');
      setCableTypes(response.data || []);
    } catch (error) {
      console.error('Errore nel caricamento tipi di cavo:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || '' : value
    }));
    
    // Rimuovi errore per questo campo
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }

    // Reset estimation quando cambiano i parametri
    if (estimation) {
      setEstimation(null);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';
    if (!formData.quantity_required || formData.quantity_required <= 0) {
      newErrors.quantity_required = 'Quantità deve essere maggiore di 0';
    }
    if (!formData.number_of_operators || formData.number_of_operators < 1) {
      newErrors.number_of_operators = 'Numero operatori deve essere almeno 1';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      setErrors({});
      
      // Prepara i dati per l'invio
      const submitData = {
        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,
        activity_type: formData.activity_type,
        quantity_required: parseFloat(formData.quantity_required),
        environmental_conditions: formData.environmental_conditions,
        tools_used: formData.tools_used,
        number_of_operators: parseInt(formData.number_of_operators),
        experience_level: formData.experience_level
      };

      const response = await axiosInstance.post('/v1/predict/estimation', submitData);
      setEstimation(response.data);
      
    } catch (error) {
      console.error('Errore nella stima:', error);
      setErrors({ 
        general: error.response?.data?.detail || 'Errore nel calcolo della stima' 
      });
    } finally {
      setLoading(false);
    }
  };

  const formatHours = (hours) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)} minuti`;
    } else if (hours < 24) {
      const h = Math.floor(hours);
      const m = Math.round((hours - h) * 60);
      return m > 0 ? `${h}h ${m}m` : `${h}h`;
    } else {
      const days = Math.floor(hours / 24);
      const h = Math.round(hours % 24);
      return h > 0 ? `${days}g ${h}h` : `${days}g`;
    }
  };

  const getCorrectionFactorColor = (factor) => {
    if (factor > 1) return 'text-green-600';
    if (factor < 1) return 'text-red-600';
    return 'text-gray-600';
  };

  const getCorrectionFactorIcon = (factor) => {
    if (factor > 1) return '↗️';
    if (factor < 1) return '↘️';
    return '➡️';
  };

  // Se non c'è un cantiere attivo, mostra un messaggio
  if (!currentCantiereId) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            Nessun cantiere attivo selezionato
          </Typography>
          <Typography variant="body2">
            Per utilizzare lo strumento di stima produttività, seleziona prima un cantiere dalla dashboard principale.
          </Typography>
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header con cantiere attivo */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>
          <Calculate sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 700 }}>
              Strumento di Stima Produttività
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 1 }}>
              <Construction sx={{ fontSize: 20, color: 'text.secondary', mr: 1 }} />
              <Typography variant="subtitle1" color="text.secondary">
                Cantiere: <strong>{currentCantiereName}</strong>
              </Typography>
              <Chip
                label={`ID: ${currentCantiereId}`}
                size="small"
                color="primary"
                variant="outlined"
                sx={{ ml: 2 }}
              />
            </Box>
          </Box>
        </Box>
        <Typography variant="subtitle1" color="text.secondary">
          Calcola tempi e costi stimati per i tuoi progetti di installazione cavi
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* Form di Input */}
        <Grid item xs={12} lg={6}>
          <Card elevation={3}>
            <CardHeader
              title="Parametri del Lavoro"
              subheader="Inserisci i dettagli del progetto per ottenere una stima accurata"
              avatar={
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <Engineering />
                </Avatar>
              }
            />
            <CardContent>
              {errors.general && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {errors.general}
                </Alert>
              )}

              <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                {/* Tipo di Attività */}
                <FormControl fullWidth error={!!errors.activity_type}>
                  <InputLabel>Tipo Attività *</InputLabel>
                  <Select
                    name="activity_type"
                    value={formData.activity_type}
                    onChange={handleInputChange}
                    label="Tipo Attività *"
                  >
                    {activityTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.activity_type && (
                    <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                      {errors.activity_type}
                    </Typography>
                  )}
                </FormControl>

                {/* Tipo di Cavo */}
                <FormControl fullWidth>
                  <InputLabel>Tipo di Cavo</InputLabel>
                  <Select
                    name="cable_type_id"
                    value={formData.cable_type_id}
                    onChange={handleInputChange}
                    label="Tipo di Cavo"
                  >
                    <MenuItem value="">Seleziona tipo di cavo (opzionale)</MenuItem>
                    {cableTypes.map(type => (
                      <MenuItem key={type.id_tipologia} value={type.id_tipologia}>
                        {type.codice_prodotto} - {type.nome_commerciale}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Quantità Richiesta */}
                <TextField
                  fullWidth
                  type="number"
                  name="quantity_required"
                  label={`Quantità Richiesta * ${formData.activity_type === 'Posa' ? '(metri)' : '(unità)'}`}
                  value={formData.quantity_required}
                  onChange={handleInputChange}
                  error={!!errors.quantity_required}
                  helperText={errors.quantity_required}
                  inputProps={{ step: 0.1, min: 0 }}
                />

                {/* Condizioni Ambientali */}
                <FormControl fullWidth>
                  <InputLabel>Condizioni Ambientali</InputLabel>
                  <Select
                    name="environmental_conditions"
                    value={formData.environmental_conditions}
                    onChange={handleInputChange}
                    label="Condizioni Ambientali"
                  >
                    {environmentalConditions.map(condition => (
                      <MenuItem key={condition.value} value={condition.value}>
                        {condition.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Strumenti Utilizzati */}
                <FormControl fullWidth>
                  <InputLabel>Strumenti Utilizzati</InputLabel>
                  <Select
                    name="tools_used"
                    value={formData.tools_used}
                    onChange={handleInputChange}
                    label="Strumenti Utilizzati"
                  >
                    {toolsUsed.map(tool => (
                      <MenuItem key={tool.value} value={tool.value}>
                        {tool.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <Grid container spacing={2}>
                  {/* Numero Operatori */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      type="number"
                      name="number_of_operators"
                      label="Numero Operatori *"
                      value={formData.number_of_operators}
                      onChange={handleInputChange}
                      error={!!errors.number_of_operators}
                      helperText={errors.number_of_operators}
                      inputProps={{ min: 1 }}
                    />
                  </Grid>

                  {/* Livello di Esperienza */}
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Livello di Esperienza</InputLabel>
                      <Select
                        name="experience_level"
                        value={formData.experience_level}
                        onChange={handleInputChange}
                        label="Livello di Esperienza"
                      >
                        {experienceLevels.map(level => (
                          <MenuItem key={level.value} value={level.value}>
                            {level.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                <Button
                  type="submit"
                  variant="contained"
                  size="large"
                  fullWidth
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Calculate />}
                  sx={{
                    py: 2,
                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',
                    }
                  }}
                >
                  {loading ? 'Calcolando...' : 'Calcola Stima'}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Risultati della Stima */}
        <Grid item xs={12} lg={6}>
          <Card elevation={3}>
            <CardHeader
              title="Risultati della Stima"
              subheader="Analisi dettagliata dei tempi e costi previsti"
              avatar={
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <Assessment />
                </Avatar>
              }
            />
            <CardContent>
              {estimation ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  {/* Risultati Principali */}
                  <Paper
                    elevation={2}
                    sx={{
                      p: 3,
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      color: 'white'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <CheckCircle sx={{ mr: 1 }} />
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Risultati Principali
                      </Typography>
                    </Box>

                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                            {formatHours(estimation.estimated_time_for_team_hours)}
                          </Typography>
                          <Typography variant="body2" sx={{ opacity: 0.8 }}>
                            Tempo stimato per il team
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                            {estimation.estimated_total_man_hours.toFixed(1)}h
                          </Typography>
                          <Typography variant="body2" sx={{ opacity: 0.8 }}>
                            Ore-uomo totali
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>

                  {/* Riepilogo Input */}
                  <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>
                      <Info sx={{ mr: 1, color: 'primary.main' }} />
                      Riepilogo Input
                    </Typography>
                    <Grid container spacing={1}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Attività:</Typography>
                        <Chip label={estimation.inputs.activity_type} size="small" color="primary" />
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Quantità:</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {estimation.inputs.quantity_required} {estimation.inputs.activity_type === 'Posa' ? 'metri' : 'unità'}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Operatori:</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {estimation.inputs.number_of_operators}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Condizioni:</Typography>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {estimation.inputs.environmental_conditions}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Paper>

                  {/* Dettagli Produttività */}
                  <Paper elevation={1} sx={{ p: 2, bgcolor: 'success.50' }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>
                      <Speed sx={{ mr: 1, color: 'success.main' }} />
                      Dettagli Produttività
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="text.secondary">Produttività base:</Typography>
                        <Typography variant="h6" sx={{ fontWeight: 600, color: 'success.main' }}>
                          {estimation.base_productivity.toFixed(1)} {estimation.inputs.activity_type === 'Posa' ? 'm/h' : 'unità/h'}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="text.secondary">Produttività attesa:</Typography>
                        <Typography variant="h6" sx={{ fontWeight: 600, color: 'success.main' }}>
                          {estimation.expected_productivity_per_operator.toFixed(1)} {estimation.inputs.activity_type === 'Posa' ? 'm/h' : 'unità/h'}/op
                        </Typography>
                      </Grid>
                    </Grid>
                  </Paper>

                  {/* Fattori di Correzione */}
                  <Paper elevation={1} sx={{ p: 2, bgcolor: 'warning.50' }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>
                      <Engineering sx={{ mr: 1, color: 'warning.main' }} />
                      Fattori di Correzione
                    </Typography>
                    <Grid container spacing={1}>
                      {Object.entries(estimation.correction_factors).map(([factor, value]) => (
                        <Grid item xs={12} sm={4} key={factor}>
                          <Box sx={{ textAlign: 'center', p: 1, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                            <Typography variant="caption" color="text.secondary" sx={{ textTransform: 'capitalize' }}>
                              {factor.replace('_', ' ')}
                            </Typography>
                            <Typography
                              variant="h6"
                              sx={{
                                fontWeight: 600,
                                color: value > 1 ? 'success.main' : value < 1 ? 'error.main' : 'text.primary'
                              }}
                            >
                              {getCorrectionFactorIcon(value)} {(value * 100).toFixed(0)}%
                            </Typography>
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </Paper>

                  {/* Note */}
                  <Alert severity="info" variant="outlined">
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      Note Importanti
                    </Typography>
                    <Box component="ul" sx={{ m: 0, pl: 2 }}>
                      <li>Le stime sono basate su dati storici e fattori di correzione</li>
                      <li>I tempi effettivi possono variare in base a condizioni specifiche</li>
                      <li>Utilizzare come riferimento per la pianificazione</li>
                    </Box>
                  </Alert>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>
                  <Calculate sx={{ fontSize: 80, mb: 2, opacity: 0.3 }} />
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    Pronto per il Calcolo
                  </Typography>
                  <Typography variant="body2">
                    Inserisci i parametri nel form a sinistra e clicca "Calcola Stima" per vedere i risultati
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EstimationTool;
