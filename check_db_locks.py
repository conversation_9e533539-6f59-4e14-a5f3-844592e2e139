#!/usr/bin/env python3
"""
Script per verificare lock e transazioni attive nel database
"""

import psycopg2
from psycopg2.extras import RealDictCursor

def check_database_status():
    """Verifica lo stato del database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="cantieri", 
            user="postgres",
            password="Taranto",
            cursor_factory=RealDictCursor
        )
        
        cursor = conn.cursor()
        
        print("🔍 VERIFICA STATO DATABASE")
        print("=" * 50)
        
        # 1. Verifica connessioni attive
        print("\n📊 1. CONNESSIONI ATTIVE")
        cursor.execute("""
            SELECT 
                pid,
                usename,
                application_name,
                client_addr,
                state,
                query_start,
                state_change,
                query
            FROM pg_stat_activity 
            WHERE datname = 'cantieri' 
            AND state != 'idle'
            ORDER BY query_start;
        """)
        
        active_connections = cursor.fetchall()
        print(f"Connessioni attive: {len(active_connections)}")
        
        for conn_info in active_connections:
            print(f"  PID {conn_info['pid']}: {conn_info['usename']} - {conn_info['state']}")
            if conn_info['query']:
                query_preview = conn_info['query'][:100] + "..." if len(conn_info['query']) > 100 else conn_info['query']
                print(f"    Query: {query_preview}")
        
        # 2. Verifica lock attivi
        print("\n🔒 2. LOCK ATTIVI")
        cursor.execute("""
            SELECT 
                l.locktype,
                l.database,
                l.relation,
                l.page,
                l.tuple,
                l.virtualxid,
                l.transactionid,
                l.classid,
                l.objid,
                l.objsubid,
                l.virtualtransaction,
                l.pid,
                l.mode,
                l.granted,
                a.usename,
                a.query,
                a.query_start,
                a.state
            FROM pg_locks l
            LEFT JOIN pg_stat_activity a ON l.pid = a.pid
            WHERE l.database = (SELECT oid FROM pg_database WHERE datname = 'cantieri')
            ORDER BY l.granted, l.pid;
        """)
        
        locks = cursor.fetchall()
        print(f"Lock attivi: {len(locks)}")
        
        # Raggruppa per tipo
        lock_types = {}
        for lock in locks:
            lock_type = lock['locktype']
            if lock_type not in lock_types:
                lock_types[lock_type] = []
            lock_types[lock_type].append(lock)
        
        for lock_type, type_locks in lock_types.items():
            print(f"  {lock_type}: {len(type_locks)} lock")
            
            # Mostra lock non granted (bloccanti)
            blocked_locks = [l for l in type_locks if not l['granted']]
            if blocked_locks:
                print(f"    ⚠️ {len(blocked_locks)} lock BLOCCATI!")
                for lock in blocked_locks:
                    print(f"      PID {lock['pid']}: {lock['mode']} su {lock['relation']}")
        
        # 3. Verifica transazioni lunghe
        print("\n⏱️ 3. TRANSAZIONI LUNGHE")
        cursor.execute("""
            SELECT 
                pid,
                usename,
                application_name,
                state,
                query_start,
                xact_start,
                now() - xact_start as duration,
                query
            FROM pg_stat_activity 
            WHERE datname = 'cantieri' 
            AND xact_start IS NOT NULL
            AND now() - xact_start > interval '30 seconds'
            ORDER BY xact_start;
        """)
        
        long_transactions = cursor.fetchall()
        print(f"Transazioni lunghe (>30s): {len(long_transactions)}")
        
        for tx in long_transactions:
            print(f"  PID {tx['pid']}: {tx['usename']} - Durata: {tx['duration']}")
            if tx['query']:
                query_preview = tx['query'][:100] + "..." if len(tx['query']) > 100 else tx['query']
                print(f"    Query: {query_preview}")
        
        # 4. Verifica deadlock
        print("\n💀 4. VERIFICA DEADLOCK")
        cursor.execute("""
            SELECT 
                blocked_locks.pid AS blocked_pid,
                blocked_activity.usename AS blocked_user,
                blocking_locks.pid AS blocking_pid,
                blocking_activity.usename AS blocking_user,
                blocked_activity.query AS blocked_statement,
                blocking_activity.query AS current_statement_in_blocking_process
            FROM pg_catalog.pg_locks blocked_locks
            JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
            JOIN pg_catalog.pg_locks blocking_locks 
                ON blocking_locks.locktype = blocked_locks.locktype
                AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
                AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
                AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
                AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
                AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
                AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
                AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
                AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
                AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
                AND blocking_locks.pid != blocked_locks.pid
            JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
            WHERE NOT blocked_locks.granted;
        """)
        
        deadlocks = cursor.fetchall()
        if deadlocks:
            print(f"⚠️ DEADLOCK RILEVATI: {len(deadlocks)}")
            for dl in deadlocks:
                print(f"  Bloccato: PID {dl['blocked_pid']} ({dl['blocked_user']})")
                print(f"  Bloccante: PID {dl['blocking_pid']} ({dl['blocking_user']})")
        else:
            print("✅ Nessun deadlock rilevato")
        
        # 5. Verifica performance query
        print("\n🐌 5. QUERY LENTE")
        cursor.execute("""
            SELECT 
                pid,
                usename,
                state,
                query_start,
                now() - query_start as duration,
                query
            FROM pg_stat_activity 
            WHERE datname = 'cantieri' 
            AND state = 'active'
            AND query_start IS NOT NULL
            AND now() - query_start > interval '5 seconds'
            ORDER BY query_start;
        """)
        
        slow_queries = cursor.fetchall()
        if slow_queries:
            print(f"⚠️ QUERY LENTE (>5s): {len(slow_queries)}")
            for sq in slow_queries:
                print(f"  PID {sq['pid']}: {sq['usename']} - Durata: {sq['duration']}")
                query_preview = sq['query'][:200] + "..." if len(sq['query']) > 200 else sq['query']
                print(f"    Query: {query_preview}")
        else:
            print("✅ Nessuna query lenta rilevata")
        
        conn.close()
        
        # Raccomandazioni
        print("\n💡 RACCOMANDAZIONI")
        print("=" * 50)
        
        if long_transactions:
            print("🔴 PROBLEMA: Transazioni lunghe rilevate")
            print("   Soluzione: Terminare le transazioni bloccanti")
            for tx in long_transactions:
                print(f"   KILL: SELECT pg_terminate_backend({tx['pid']});")
        
        if deadlocks:
            print("🔴 PROBLEMA: Deadlock rilevati")
            print("   Soluzione: Terminare i processi bloccanti")
            for dl in deadlocks:
                print(f"   KILL: SELECT pg_terminate_backend({dl['blocking_pid']});")
        
        if slow_queries:
            print("🔴 PROBLEMA: Query lente in esecuzione")
            print("   Soluzione: Ottimizzare o terminare le query")
            for sq in slow_queries:
                print(f"   KILL: SELECT pg_terminate_backend({sq['pid']});")
        
        if not (long_transactions or deadlocks or slow_queries):
            print("✅ Database in stato normale")
            print("   Il problema potrebbe essere:")
            print("   - Configurazione timeout applicazione")
            print("   - Problemi di rete")
            print("   - Carico CPU/memoria")
        
    except Exception as e:
        print(f"❌ Errore: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database_status()
