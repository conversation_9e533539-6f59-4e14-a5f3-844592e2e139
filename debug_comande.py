#!/usr/bin/env python3
"""
Script di debug per testare la creazione delle comande
"""

import sys
import os
import logging
from datetime import datetime

# Aggiungi il percorso dei moduli
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

# Configura logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_database_connection():
    """Test della connessione al database"""
    try:
        from modules.database_pg import database_connection
        
        print("🔍 Test connessione database...")
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("SELECT 1 as test")
            result = c.fetchone()
            print(f"✅ Connessione database OK: {result}")
            return True
    except Exception as e:
        print(f"❌ Errore connessione database: {e}")
        return False

def test_cantieri():
    """Test per verificare i cantieri disponibili"""
    try:
        from modules.database_pg import database_connection
        
        print("\n🔍 Test cantieri disponibili...")
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("SELECT id_cantiere, nome_cantiere FROM Cantieri LIMIT 5")
            cantieri = c.fetchall()
            
            if cantieri:
                print("✅ Cantieri trovati:")
                for cantiere in cantieri:
                    print(f"   - ID: {cantiere['id_cantiere']}, Nome: {cantiere['nome_cantiere']}")
                return cantieri[0]['id_cantiere']  # Restituisce il primo ID
            else:
                print("❌ Nessun cantiere trovato")
                return None
    except Exception as e:
        print(f"❌ Errore nel recupero cantieri: {e}")
        return None

def test_cavi_disponibili(id_cantiere):
    """Test per verificare i cavi disponibili"""
    try:
        from modules.database_pg import database_connection
        
        print(f"\n🔍 Test cavi disponibili per cantiere {id_cantiere}...")
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT id_cavo, tipologia, sezione, metri_teorici, stato_installazione, 
                       comanda_posa, comanda_partenza, comanda_arrivo, comanda_certificazione
                FROM Cavi 
                WHERE id_cantiere = %s 
                LIMIT 10
            """, (id_cantiere,))
            cavi = c.fetchall()
            
            if cavi:
                print(f"✅ Trovati {len(cavi)} cavi:")
                for cavo in cavi[:3]:  # Mostra solo i primi 3
                    print(f"   - {cavo['id_cavo']}: {cavo['tipologia']} {cavo['sezione']} - {cavo['stato_installazione']}")
                
                # Trova cavi disponibili per POSA
                cavi_posa = [c for c in cavi if not c['comanda_posa'] and c['metri_teorici'] > 0]
                print(f"✅ Cavi disponibili per POSA: {len(cavi_posa)}")
                
                return [c['id_cavo'] for c in cavi_posa[:2]]  # Restituisce i primi 2 ID
            else:
                print("❌ Nessun cavo trovato")
                return []
    except Exception as e:
        print(f"❌ Errore nel recupero cavi: {e}")
        return []

def test_crea_comanda_semplice(id_cantiere):
    """Test creazione comanda semplice"""
    try:
        from modules.comande_new import crea_comanda
        
        print(f"\n🔍 Test creazione comanda semplice per cantiere {id_cantiere}...")
        
        codice_comanda = crea_comanda(
            id_cantiere=id_cantiere,
            tipo_comanda="POSA",
            descrizione="Test comanda debug",
            responsabile="Test Debug User",
            numero_componenti_squadra=1
        )
        
        if codice_comanda:
            print(f"✅ Comanda creata con successo: {codice_comanda}")
            return codice_comanda
        else:
            print("❌ Errore nella creazione della comanda")
            return None
    except Exception as e:
        print(f"❌ Errore nella creazione comanda: {e}")
        return None

def test_crea_comanda_con_cavi(id_cantiere, lista_cavi):
    """Test creazione comanda con cavi"""
    try:
        from modules.comande_new import crea_comanda_con_cavi
        
        print(f"\n🔍 Test creazione comanda con cavi per cantiere {id_cantiere}...")
        print(f"   Cavi da assegnare: {lista_cavi}")
        
        codice_comanda = crea_comanda_con_cavi(
            id_cantiere=id_cantiere,
            tipo_comanda="POSA",
            descrizione="Test comanda con cavi debug",
            responsabile="Test Debug User",
            lista_id_cavi=lista_cavi,
            numero_componenti_squadra=1
        )
        
        if codice_comanda:
            print(f"✅ Comanda con cavi creata con successo: {codice_comanda}")
            return codice_comanda
        else:
            print("❌ Errore nella creazione della comanda con cavi")
            return None
    except Exception as e:
        print(f"❌ Errore nella creazione comanda con cavi: {e}")
        return None

def test_responsabili(id_cantiere):
    """Test per verificare i responsabili"""
    try:
        from modules.database_pg import database_connection
        
        print(f"\n🔍 Test responsabili per cantiere {id_cantiere}...")
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT id_responsabile, nome_responsabile, numero_telefono, mail
                FROM Responsabili 
                WHERE id_cantiere = %s
            """, (id_cantiere,))
            responsabili = c.fetchall()
            
            if responsabili:
                print(f"✅ Trovati {len(responsabili)} responsabili:")
                for resp in responsabili:
                    print(f"   - {resp['nome_responsabile']}: {resp['numero_telefono']} - {resp['mail']}")
            else:
                print("⚠️ Nessun responsabile trovato")
            
            return responsabili
    except Exception as e:
        print(f"❌ Errore nel recupero responsabili: {e}")
        return []

def main():
    """Funzione principale di test"""
    print("🚀 Inizio test debug comande")
    print("=" * 50)
    
    # Test 1: Connessione database
    if not test_database_connection():
        print("❌ Test fallito: impossibile connettersi al database")
        return
    
    # Test 2: Cantieri
    id_cantiere = test_cantieri()
    if not id_cantiere:
        print("❌ Test fallito: nessun cantiere disponibile")
        return
    
    # Test 3: Responsabili
    test_responsabili(id_cantiere)
    
    # Test 4: Cavi disponibili
    lista_cavi = test_cavi_disponibili(id_cantiere)
    if not lista_cavi:
        print("⚠️ Nessun cavo disponibile per test")
    
    # Test 5: Creazione comanda semplice
    comanda_semplice = test_crea_comanda_semplice(id_cantiere)
    
    # Test 6: Creazione comanda con cavi (se ci sono cavi disponibili)
    if lista_cavi:
        comanda_con_cavi = test_crea_comanda_con_cavi(id_cantiere, lista_cavi)
    
    print("\n" + "=" * 50)
    print("🏁 Test completati")

if __name__ == "__main__":
    main()
