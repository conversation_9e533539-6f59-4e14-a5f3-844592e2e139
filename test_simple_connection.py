#!/usr/bin/env python3
"""
Test semplice per verificare la connessione al database senza inizializzazione
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import os

def test_direct_connection():
    """Test connessione diretta al database"""
    try:
        # Parametri di connessione
        conn_params = {
            'host': os.environ.get('DB_HOST', 'localhost'),
            'port': os.environ.get('DB_PORT', '5432'),
            'dbname': os.environ.get('DB_NAME', 'cantieri'),
            'user': os.environ.get('DB_USER', 'postgres'),
            'password': os.environ.get('DB_PASSWORD', 'Taranto')
        }
        
        print(f"🔍 Test connessione diretta con parametri: {conn_params}")
        
        # Connessione diretta
        conn = psycopg2.connect(**conn_params)
        conn.cursor_factory = RealDictCursor
        cursor = conn.cursor()
        
        # Test semplice
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        print(f"✅ Connessione OK: {result}")
        
        # Test tabelle esistenti
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        """)
        tables = cursor.fetchall()
        print(f"📋 Tabelle trovate: {len(tables)}")
        for table in tables[:10]:  # Mostra solo le prime 10
            print(f"   - {table['table_name']}")
        
        # Test cantieri
        cursor.execute("SELECT COUNT(*) as count FROM Cantieri")
        cantieri_count = cursor.fetchone()
        print(f"🏗️ Cantieri nel database: {cantieri_count['count']}")
        
        if cantieri_count['count'] > 0:
            # Prima verifica le colonne disponibili
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'cantieri'
                ORDER BY ordinal_position
            """)
            columns = cursor.fetchall()
            print(f"   Colonne tabella Cantieri: {[c['column_name'] for c in columns]}")

            cursor.execute("SELECT id_cantiere, commessa, descrizione FROM Cantieri LIMIT 3")
            cantieri = cursor.fetchall()
            print("   Primi cantieri:")
            for cantiere in cantieri:
                print(f"   - ID: {cantiere['id_cantiere']}, Commessa: {cantiere['commessa']}, Desc: {cantiere['descrizione']}")
        
        # Test cavi
        cursor.execute("SELECT COUNT(*) as count FROM Cavi")
        cavi_count = cursor.fetchone()
        print(f"🔌 Cavi nel database: {cavi_count['count']}")
        
        # Test comande
        cursor.execute("SELECT COUNT(*) as count FROM Comande")
        comande_count = cursor.fetchone()
        print(f"📋 Comande nel database: {comande_count['count']}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Errore connessione: {e}")
        return False

def test_create_simple_comanda():
    """Test creazione comanda semplice senza moduli"""
    try:
        # Parametri di connessione
        conn_params = {
            'host': os.environ.get('DB_HOST', 'localhost'),
            'port': os.environ.get('DB_PORT', '5432'),
            'dbname': os.environ.get('DB_NAME', 'cantieri'),
            'user': os.environ.get('DB_USER', 'postgres'),
            'password': os.environ.get('DB_PASSWORD', 'Taranto')
        }
        
        conn = psycopg2.connect(**conn_params)
        conn.cursor_factory = RealDictCursor
        cursor = conn.cursor()
        
        # Trova un cantiere esistente
        cursor.execute("SELECT id_cantiere FROM Cantieri LIMIT 1")
        cantiere = cursor.fetchone()
        
        if not cantiere:
            print("❌ Nessun cantiere trovato")
            return False
        
        id_cantiere = cantiere['id_cantiere']
        print(f"🏗️ Usando cantiere ID: {id_cantiere}")
        
        # Genera codice comanda semplice
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        codice_comanda = f"TEST_POSA_{id_cantiere}_{timestamp}"
        
        print(f"📋 Tentativo creazione comanda: {codice_comanda}")
        
        # Inserisci comanda
        cursor.execute("""
            INSERT INTO Comande (
                codice_comanda, tipo_comanda, descrizione, data_creazione,
                responsabile, stato, id_cantiere, numero_componenti_squadra
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            codice_comanda, "POSA", "Test comanda diretta", datetime.now().date(),
            "Test User", "CREATA", id_cantiere, 1
        ))
        
        conn.commit()
        print(f"✅ Comanda creata con successo: {codice_comanda}")
        
        # Verifica creazione
        cursor.execute("SELECT * FROM Comande WHERE codice_comanda = %s", (codice_comanda,))
        comanda_creata = cursor.fetchone()
        print(f"✅ Verifica: {dict(comanda_creata)}")
        
        cursor.close()
        conn.close()
        
        return codice_comanda
        
    except Exception as e:
        print(f"❌ Errore creazione comanda: {e}")
        return None

def main():
    """Funzione principale"""
    print("🚀 Test connessione database semplice")
    print("=" * 50)
    
    # Test 1: Connessione diretta
    if not test_direct_connection():
        print("❌ Test fallito: connessione database")
        return
    
    print("\n" + "-" * 30)
    
    # Test 2: Creazione comanda diretta
    codice_comanda = test_create_simple_comanda()
    
    if codice_comanda:
        print(f"\n✅ Test completato con successo!")
        print(f"   Comanda creata: {codice_comanda}")
    else:
        print(f"\n❌ Test fallito nella creazione comanda")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
