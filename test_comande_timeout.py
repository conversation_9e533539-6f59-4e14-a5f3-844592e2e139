#!/usr/bin/env python3
"""
Script per testare il problema di timeout delle comande
"""

import requests
import time
import json
from datetime import datetime

# Configurazione
BASE_URL = "http://localhost:8001/api"
USERNAME = "admin"
PASSWORD = "admin"

def login():
    """Effettua il login e restituisce il token"""
    try:
        login_data = {
            "username": USERNAME,
            "password": PASSWORD
        }

        print(f"🔐 Tentativo di login con {USERNAME}...")
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data, timeout=10)
        
        if response.status_code == 200:
            token = response.json().get("access_token")
            print(f"✅ Login riuscito! Token ottenuto.")
            return token
        else:
            print(f"❌ Login fallito: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Errore durante il login: {str(e)}")
        return None

def test_comande_api(token):
    """Testa l'API delle comande"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\n🧪 TEST API COMANDE")
    print("=" * 50)
    
    # Test 1: Lista comande cantiere 1
    print("\n📋 Test 1: Lista comande cantiere 1")
    start_time = time.time()
    
    try:
        response = requests.get(
            f"{BASE_URL}/comande/cantiere/1", 
            headers=headers, 
            timeout=30
        )
        
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000
        
        print(f"⏱️ Tempo di risposta: {execution_time:.2f}ms")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Successo! Trovate {len(data.get('comande', []))} comande")
            
            # Mostra le prime 3 comande
            comande = data.get('comande', [])
            for i, comanda in enumerate(comande[:3]):
                print(f"  {i+1}. {comanda.get('codice_comanda')} - {comanda.get('tipo_comanda')} - {comanda.get('stato')}")
                
        else:
            print(f"❌ Errore: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ TIMEOUT! La richiesta ha impiegato più di 30 secondi")
        return False
    except Exception as e:
        print(f"❌ Errore: {str(e)}")
        return False
    
    # Test 2: Dettagli di una comanda specifica
    print("\n📋 Test 2: Dettagli comanda specifica")
    start_time = time.time()
    
    try:
        # Prima ottieni una comanda esistente
        response = requests.get(f"{BASE_URL}/comande/cantiere/1", headers=headers, timeout=10)
        if response.status_code == 200:
            comande = response.json().get('comande', [])
            if comande:
                codice_comanda = comande[0]['codice_comanda']
                print(f"🔍 Testando dettagli per comanda: {codice_comanda}")
                
                response = requests.get(
                    f"{BASE_URL}/comande/{codice_comanda}", 
                    headers=headers, 
                    timeout=30
                )
                
                end_time = time.time()
                execution_time = (end_time - start_time) * 1000
                
                print(f"⏱️ Tempo di risposta: {execution_time:.2f}ms")
                print(f"📊 Status code: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ Dettagli comanda ottenuti con successo")
                else:
                    print(f"❌ Errore: {response.text}")
            else:
                print("⚠️ Nessuna comanda trovata per il test")
        
    except requests.exceptions.Timeout:
        print("⏰ TIMEOUT! La richiesta ha impiegato più di 30 secondi")
        return False
    except Exception as e:
        print(f"❌ Errore: {str(e)}")
        return False
    
    # Test 3: Statistiche comande
    print("\n📋 Test 3: Statistiche comande")
    start_time = time.time()
    
    try:
        response = requests.get(
            f"{BASE_URL}/comande/cantiere/1/statistiche", 
            headers=headers, 
            timeout=30
        )
        
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000
        
        print(f"⏱️ Tempo di risposta: {execution_time:.2f}ms")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            stats = response.json()
            print("✅ Statistiche ottenute con successo")
            print(f"  Totale comande: {stats.get('totale_comande', 'N/A')}")
            print(f"  Comande completate: {stats.get('comande_completate', 'N/A')}")
        else:
            print(f"❌ Errore: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ TIMEOUT! La richiesta ha impiegato più di 30 secondi")
        return False
    except Exception as e:
        print(f"❌ Errore: {str(e)}")
        return False
    
    return True

def main():
    """Funzione principale"""
    print("🔍 DIAGNOSI PROBLEMA TIMEOUT COMANDE")
    print("=" * 60)
    print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Login
    token = login()
    if not token:
        print("\n❌ Impossibile procedere senza autenticazione")
        return
    
    # Step 2: Test API
    success = test_comande_api(token)
    
    # Step 3: Conclusioni
    print("\n📊 CONCLUSIONI")
    print("=" * 50)
    
    if success:
        print("✅ Tutti i test sono stati completati con successo")
        print("💡 Il problema potrebbe essere lato frontend o nella gestione delle richieste")
    else:
        print("❌ Rilevati problemi di timeout nell'API")
        print("💡 Il problema è lato backend - verificare:")
        print("   - Connessioni al database")
        print("   - Query lente")
        print("   - Deadlock o lock di tabelle")
        print("   - Memoria insufficiente")

if __name__ == "__main__":
    main()
