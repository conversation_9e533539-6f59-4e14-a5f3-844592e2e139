import React, { useState, useEffect } from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  Chip,
  Avatar,
  LinearProgress,
  Divider,
  Paper,
  IconButton,
  Tooltip as MuiTooltip,
  Alert
} from '@mui/material';
import {
  TrendingUp,
  Schedule,
  Engineering,
  Speed,
  Assessment,
  FilterList,
  Refresh,
  Download,
  Construction
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import axiosInstance from '../../services/axiosConfig';

// Registra i componenti Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const Dashboard = () => {
  const { selectedCantiere: authSelectedCantiere } = useAuth();
  const [workLogs, setWorkLogs] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 giorni fa
    end: new Date().toISOString().split('T')[0] // oggi
  });

  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage
  const currentCantiereId = authSelectedCantiere?.id_cantiere ||
                           parseInt(localStorage.getItem('selectedCantiereId'), 10) ||
                           null;
  const currentCantiereName = authSelectedCantiere?.commessa ||
                             localStorage.getItem('selectedCantiereName') ||
                             'Cantiere non selezionato';

  useEffect(() => {
    if (currentCantiereId) {
      loadDashboardData();
    }
  }, [currentCantiereId, dateRange]);

  // Carica i dati solo se c'è un cantiere attivo
  useEffect(() => {
    if (!currentCantiereId) {
      setLoading(false);
    }
  }, [currentCantiereId]);

  const loadDashboardData = async () => {
    if (!currentCantiereId) {
      console.warn('Nessun cantiere attivo selezionato per la produttività');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Parametri per le chiamate API - usa solo il cantiere attivo
      const params = {
        id_cantiere: currentCantiereId,
        start_date: dateRange.start,
        end_date: dateRange.end,
        per_page: 100
      };

      console.log('Caricamento dati produttività per cantiere:', currentCantiereId, currentCantiereName);

      // Carica work logs per il cantiere attivo
      const workLogsRes = await axiosInstance.get('/v1/work-logs', { params });
      setWorkLogs(workLogsRes.data.work_logs || []);

      // Calcola statistiche
      calculateStatistics(workLogsRes.data.work_logs || []);

    } catch (error) {
      console.error('Errore nel caricamento dati dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStatistics = (logs) => {
    const stats = {
      totalLogs: logs.length,
      totalQuantity: 0,
      totalManHours: 0,
      averageProductivity: 0,
      byActivity: {},
      byOperator: {},
      byConditions: {},
      dailyTrend: {},
      productivityTrend: []
    };

    logs.forEach(log => {
      // Totali
      stats.totalQuantity += log.quantity || 0;
      stats.totalManHours += log.total_man_hours || 0;

      // Per attività
      if (!stats.byActivity[log.activity_type]) {
        stats.byActivity[log.activity_type] = {
          count: 0,
          quantity: 0,
          manHours: 0,
          productivity: 0
        };
      }
      stats.byActivity[log.activity_type].count++;
      stats.byActivity[log.activity_type].quantity += log.quantity || 0;
      stats.byActivity[log.activity_type].manHours += log.total_man_hours || 0;

      // Per operatore
      const operatorKey = `${log.operator_id}`;
      if (!stats.byOperator[operatorKey]) {
        stats.byOperator[operatorKey] = {
          count: 0,
          quantity: 0,
          manHours: 0,
          productivity: 0
        };
      }
      stats.byOperator[operatorKey].count++;
      stats.byOperator[operatorKey].quantity += log.quantity || 0;
      stats.byOperator[operatorKey].manHours += log.total_man_hours || 0;

      // Per condizioni ambientali
      if (!stats.byConditions[log.environmental_conditions]) {
        stats.byConditions[log.environmental_conditions] = {
          count: 0,
          quantity: 0,
          productivity: 0
        };
      }
      stats.byConditions[log.environmental_conditions].count++;
      stats.byConditions[log.environmental_conditions].quantity += log.quantity || 0;

      // Trend giornaliero
      const date = new Date(log.start_timestamp).toISOString().split('T')[0];
      if (!stats.dailyTrend[date]) {
        stats.dailyTrend[date] = {
          quantity: 0,
          manHours: 0,
          productivity: 0
        };
      }
      stats.dailyTrend[date].quantity += log.quantity || 0;
      stats.dailyTrend[date].manHours += log.total_man_hours || 0;
    });

    // Calcola produttività medie
    stats.averageProductivity = stats.totalManHours > 0 ? stats.totalQuantity / stats.totalManHours : 0;

    Object.keys(stats.byActivity).forEach(activity => {
      const data = stats.byActivity[activity];
      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;
    });

    Object.keys(stats.byOperator).forEach(operator => {
      const data = stats.byOperator[operator];
      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;
    });

    Object.keys(stats.byConditions).forEach(condition => {
      const data = stats.byConditions[condition];
      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;
    });

    Object.keys(stats.dailyTrend).forEach(date => {
      const data = stats.dailyTrend[date];
      data.productivity = data.manHours > 0 ? data.quantity / data.manHours : 0;
    });

    setStatistics(stats);
  };

  // Configurazioni grafici
  const productivityByActivityChart = {
    labels: Object.keys(statistics.byActivity || {}),
    datasets: [
      {
        label: 'Produttività (unità/ora)',
        data: Object.values(statistics.byActivity || {}).map(d => d.productivity.toFixed(2)),
        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],
        borderColor: ['#2563EB', '#059669', '#D97706', '#DC2626'],
        borderWidth: 1,
      },
    ],
  };

  const dailyTrendChart = {
    labels: Object.keys(statistics.dailyTrend || {}).sort(),
    datasets: [
      {
        label: 'Quantità Giornaliera',
        data: Object.keys(statistics.dailyTrend || {}).sort().map(date => 
          statistics.dailyTrend[date].quantity
        ),
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.1,
      },
      {
        label: 'Produttività Giornaliera',
        data: Object.keys(statistics.dailyTrend || {}).sort().map(date => 
          statistics.dailyTrend[date].productivity
        ),
        borderColor: '#10B981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.1,
        yAxisID: 'y1',
      },
    ],
  };

  const conditionsChart = {
    labels: Object.keys(statistics.byConditions || {}),
    datasets: [
      {
        data: Object.values(statistics.byConditions || {}).map(d => d.count),
        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  if (loading) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <LinearProgress sx={{ mb: 2 }} />
        <Typography variant="h6" color="text.secondary">
          Caricamento dashboard...
        </Typography>
      </Box>
    );
  }

  // Se non c'è un cantiere attivo, mostra un messaggio
  if (!currentCantiereId) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            Nessun cantiere attivo selezionato
          </Typography>
          <Typography variant="body2">
            Per visualizzare i dati di produttività, seleziona prima un cantiere dalla dashboard principale.
          </Typography>
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header con cantiere attivo */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Assessment sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
            <Box>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 700 }}>
                Dashboard Produttività
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Construction sx={{ fontSize: 20, color: 'text.secondary', mr: 1 }} />
                <Typography variant="subtitle1" color="text.secondary">
                  Cantiere: <strong>{currentCantiereName}</strong>
                </Typography>
                <Chip
                  label={`ID: ${currentCantiereId}`}
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{ ml: 2 }}
                />
              </Box>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <MuiTooltip title="Aggiorna dati">
              <IconButton onClick={loadDashboardData} color="primary">
                <Refresh />
              </IconButton>
            </MuiTooltip>
            <MuiTooltip title="Esporta report">
              <IconButton color="primary">
                <Download />
              </IconButton>
            </MuiTooltip>
          </Box>
        </Box>

        {/* Filtri temporali */}
        <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.50' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
            <FilterList sx={{ color: 'text.secondary' }} />
            <Typography variant="subtitle2" color="text.secondary" sx={{ mr: 2 }}>
              Periodo di analisi:
            </Typography>

            <TextField
              type="date"
              label="Data inizio"
              size="small"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              InputLabelProps={{ shrink: true }}
            />

            <TextField
              type="date"
              label="Data fine"
              size="small"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              InputLabelProps={{ shrink: true }}
            />
          </Box>
        </Paper>
      </Box>

      {/* KPI Cards moderne */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={3}
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              position: 'relative',
              overflow: 'visible'
            }}
          >
            <CardContent sx={{ pb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
                    Work Logs Totali
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    {statistics.totalLogs}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <Engineering sx={{ fontSize: 28 }} />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={3}
            sx={{
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              color: 'white'
            }}
          >
            <CardContent sx={{ pb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
                    Quantità Totale
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    {statistics.totalQuantity?.toFixed(1)}
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    metri/unità
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <TrendingUp sx={{ fontSize: 28 }} />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={3}
            sx={{
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
              color: 'white'
            }}
          >
            <CardContent sx={{ pb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
                    Ore-Uomo Totali
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    {statistics.totalManHours?.toFixed(1)}
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    ore lavorate
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <Schedule sx={{ fontSize: 28 }} />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            elevation={3}
            sx={{
              background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
              color: 'white'
            }}
          >
            <CardContent sx={{ pb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
                    Produttività Media
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    {statistics.averageProductivity?.toFixed(2)}
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    unità/ora
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <Speed sx={{ fontSize: 28 }} />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Grafici moderni */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Produttività per Attività */}
        <Grid item xs={12} lg={8}>
          <Card elevation={3}>
            <CardHeader
              title="Produttività per Attività"
              subheader="Confronto performance tra diverse tipologie di lavoro"
              avatar={
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <TrendingUp />
                </Avatar>
              }
            />
            <CardContent>
              {Object.keys(statistics.byActivity || {}).length > 0 ? (
                <Box sx={{ height: 400 }}>
                  <Bar data={productivityByActivityChart} options={{
                    ...chartOptions,
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false
                      }
                    }
                  }} />
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>
                  <Assessment sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />
                  <Typography variant="h6">Nessun dato disponibile</Typography>
                  <Typography variant="body2">
                    Aggiungi dei work logs per visualizzare le statistiche
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Distribuzione Condizioni Ambientali */}
        <Grid item xs={12} lg={4}>
          <Card elevation={3}>
            <CardHeader
              title="Condizioni Ambientali"
              subheader="Distribuzione dei lavori per ambiente"
              avatar={
                <Avatar sx={{ bgcolor: 'secondary.main' }}>
                  <Assessment />
                </Avatar>
              }
            />
            <CardContent>
              {Object.keys(statistics.byConditions || {}).length > 0 ? (
                <Box sx={{ height: 300, display: 'flex', justifyContent: 'center' }}>
                  <Doughnut data={conditionsChart} options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom'
                      }
                    }
                  }} />
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
                  <Assessment sx={{ fontSize: 48, mb: 1, opacity: 0.3 }} />
                  <Typography variant="body2">Nessun dato disponibile</Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Trend Giornaliero */}
      <Card elevation={3} sx={{ mb: 4 }}>
        <CardHeader
          title="Trend Giornaliero"
          subheader="Andamento della produttività nel tempo"
          avatar={
            <Avatar sx={{ bgcolor: 'success.main' }}>
              <TrendingUp />
            </Avatar>
          }
        />
        <CardContent>
          {Object.keys(statistics.dailyTrend || {}).length > 0 ? (
            <Box sx={{ height: 400 }}>
              <Line data={dailyTrendChart} options={{
                ...chartOptions,
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true,
                    title: {
                      display: true,
                      text: 'Quantità'
                    }
                  },
                  y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                      display: true,
                      text: 'Produttività'
                    },
                    grid: {
                      drawOnChartArea: false,
                    },
                  },
                }
              }} />
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>
              <TrendingUp sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />
              <Typography variant="h6">Nessun trend disponibile</Typography>
              <Typography variant="body2">
                I dati del trend appariranno dopo alcuni giorni di lavoro
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Tabella Dettagli per Attività */}
      <Card elevation={3}>
        <CardHeader
          title="Dettagli per Attività"
          subheader="Analisi dettagliata delle performance per tipologia di lavoro"
          avatar={
            <Avatar sx={{ bgcolor: 'info.main' }}>
              <Assessment />
            </Avatar>
          }
        />
        <CardContent sx={{ p: 0 }}>
          {Object.keys(statistics.byActivity || {}).length > 0 ? (
            <Box sx={{ overflow: 'auto' }}>
              <Box component="table" sx={{ width: '100%', borderCollapse: 'collapse' }}>
                <Box component="thead" sx={{ bgcolor: 'grey.50' }}>
                  <Box component="tr">
                    <Box component="th" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>
                      Attività
                    </Box>
                    <Box component="th" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>
                      Work Logs
                    </Box>
                    <Box component="th" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>
                      Quantità Totale
                    </Box>
                    <Box component="th" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>
                      Ore-Uomo
                    </Box>
                    <Box component="th" sx={{ p: 2, textAlign: 'left', fontWeight: 600, color: 'text.secondary' }}>
                      Produttività
                    </Box>
                  </Box>
                </Box>
                <Box component="tbody">
                  {Object.entries(statistics.byActivity || {}).map(([activity, data], index) => (
                    <Box
                      component="tr"
                      key={activity}
                      sx={{
                        '&:hover': { bgcolor: 'grey.50' },
                        borderBottom: index < Object.keys(statistics.byActivity).length - 1 ? '1px solid' : 'none',
                        borderColor: 'divider'
                      }}
                    >
                      <Box component="td" sx={{ p: 2 }}>
                        <Chip
                          label={activity}
                          color="primary"
                          variant="outlined"
                          size="small"
                        />
                      </Box>
                      <Box component="td" sx={{ p: 2, fontWeight: 500 }}>
                        {data.count}
                      </Box>
                      <Box component="td" sx={{ p: 2 }}>
                        {data.quantity.toFixed(1)}
                      </Box>
                      <Box component="td" sx={{ p: 2 }}>
                        {data.manHours.toFixed(1)}
                      </Box>
                      <Box component="td" sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {data.productivity.toFixed(2)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            unità/ora
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  ))}
                </Box>
              </Box>
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center', py: 8, color: 'text.secondary' }}>
              <Assessment sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />
              <Typography variant="h6">Nessun dettaglio disponibile</Typography>
              <Typography variant="body2">
                I dettagli appariranno dopo aver registrato dei work logs
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default Dashboard;
