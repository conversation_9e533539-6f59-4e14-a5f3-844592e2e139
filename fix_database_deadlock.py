#!/usr/bin/env python3
"""
Script per risolvere il deadlock nel database
"""

import psycopg2
from psycopg2.extras import RealDictCursor

def fix_deadlock():
    """Risolve il deadlock terminando le transazioni bloccanti"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="cantieri", 
            user="postgres",
            password="Taranto",
            cursor_factory=RealDictCursor
        )
        
        cursor = conn.cursor()
        
        print("🔧 RISOLUZIONE DEADLOCK DATABASE")
        print("=" * 50)
        
        # PIDs da terminare (dalle analisi precedenti)
        pids_to_kill = [18972, 15884, 17304]
        
        for pid in pids_to_kill:
            print(f"\n🔪 Terminando processo PID {pid}...")
            
            try:
                cursor.execute("SELECT pg_terminate_backend(%s);", (pid,))
                result = cursor.fetchone()
                
                if result and result[0]:
                    print(f"✅ Processo {pid} terminato con successo")
                else:
                    print(f"⚠️ Processo {pid} non trovato o già terminato")
                    
            except Exception as e:
                print(f"❌ Errore terminando processo {pid}: {str(e)}")
        
        conn.commit()
        
        # Verifica che i processi siano stati terminati
        print(f"\n🔍 Verifica stato dopo la pulizia...")
        
        cursor.execute("""
            SELECT 
                pid,
                usename,
                state,
                query_start,
                now() - query_start as duration
            FROM pg_stat_activity 
            WHERE datname = 'cantieri' 
            AND state != 'idle'
            ORDER BY query_start;
        """)
        
        remaining_connections = cursor.fetchall()
        print(f"Connessioni attive rimanenti: {len(remaining_connections)}")
        
        for conn_info in remaining_connections:
            print(f"  PID {conn_info['pid']}: {conn_info['usename']} - {conn_info['state']}")
        
        # Verifica lock rimanenti
        cursor.execute("""
            SELECT COUNT(*) as lock_count
            FROM pg_locks l
            WHERE l.database = (SELECT oid FROM pg_database WHERE datname = 'cantieri')
            AND NOT l.granted;
        """)
        
        blocked_locks = cursor.fetchone()['lock_count']
        
        if blocked_locks == 0:
            print("✅ Tutti i lock bloccanti sono stati risolti")
        else:
            print(f"⚠️ Rimangono {blocked_locks} lock bloccanti")
        
        conn.close()
        
        print("\n💡 STATO FINALE")
        print("=" * 50)
        print("✅ Operazione di pulizia completata")
        print("🔄 Il sistema dovrebbe ora funzionare normalmente")
        print("📋 Prova a ricaricare la pagina delle comande")
        
    except Exception as e:
        print(f"❌ Errore durante la risoluzione: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_deadlock()
