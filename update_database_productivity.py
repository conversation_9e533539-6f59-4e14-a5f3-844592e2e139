#!/usr/bin/env python3
"""
Script per aggiornare il database con le nuove tabelle e campi per il sistema di produttività.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from modules.database_pg import Database
import logging

# Configura logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def update_database_for_productivity():
    """Aggiorna il database per supportare il sistema di produttività."""
    try:
        print("🔄 AGGIORNAMENTO DATABASE PER SISTEMA PRODUTTIVITÀ")
        print("=" * 60)
        
        # Inizializza il database (questo creerà le nuove tabelle e campi)
        db = Database()
        
        print("✅ Database aggiornato con successo!")
        print("\n📋 MODIFICHE APPLICATE:")
        print("- ✅ Aggiunta tabella work_logs")
        print("- ✅ Aggiunti campi base_productivity_* a tipologie_cavi")
        print("- ✅ Aggiunto campo experience_level a responsabili")
        print("- ✅ Creati indici per ottimizzazione performance")
        
        print("\n🎯 PROSSIMI PASSI:")
        print("1. Avviare il backend: python webapp/run_system_simple.py")
        print("2. Testare le API di produttività: /api/v1/work-logs")
        print("3. Implementare il frontend React")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ Errore durante l'aggiornamento: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_productivity_tables():
    """Testa che le nuove tabelle siano state create correttamente."""
    try:
        print("\n🧪 TEST TABELLE PRODUTTIVITÀ")
        print("=" * 40)
        
        from modules.database_pg import database_connection
        
        with database_connection() as (conn, cursor):
            
            # Test tabella work_logs
            cursor.execute("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'work_logs'
                ORDER BY ordinal_position
            """)
            work_logs_columns = cursor.fetchall()
            
            if work_logs_columns:
                print("✅ Tabella work_logs creata con successo")
                print(f"   Colonne: {len(work_logs_columns)}")
                for col_name, col_type in work_logs_columns[:5]:  # Mostra prime 5
                    print(f"   - {col_name}: {col_type}")
                if len(work_logs_columns) > 5:
                    print(f"   ... e altre {len(work_logs_columns) - 5} colonne")
            else:
                print("❌ Tabella work_logs non trovata")
            
            # Test campi produttività in tipologie_cavi
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'tipologie_cavi' 
                AND column_name LIKE '%productivity%'
            """)
            productivity_columns = cursor.fetchall()
            
            if productivity_columns:
                print("✅ Campi produttività aggiunti a tipologie_cavi")
                for col_name, in productivity_columns:
                    print(f"   - {col_name}")
            else:
                print("❌ Campi produttività non trovati in tipologie_cavi")
            
            # Test campo experience_level in responsabili
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'responsabili' 
                AND column_name = 'experience_level'
            """)
            experience_column = cursor.fetchone()
            
            if experience_column:
                print("✅ Campo experience_level aggiunto a responsabili")
            else:
                print("❌ Campo experience_level non trovato in responsabili")
            
            # Test indici
            cursor.execute("""
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = 'work_logs'
            """)
            work_logs_indexes = cursor.fetchall()
            
            if work_logs_indexes:
                print(f"✅ Indici creati per work_logs: {len(work_logs_indexes)}")
                for idx_name, in work_logs_indexes:
                    print(f"   - {idx_name}")
            else:
                print("⚠️ Nessun indice trovato per work_logs")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ Errore durante il test: {str(e)}")
        return False

def insert_sample_productivity_data():
    """Inserisce dati di esempio per testare il sistema di produttività."""
    try:
        print("\n📊 INSERIMENTO DATI DI ESEMPIO")
        print("=" * 40)
        
        from modules.database_pg import database_connection
        
        with database_connection() as (conn, cursor):
            
            # Aggiorna alcune tipologie di cavi con valori di produttività base
            sample_productivity_data = [
                ("FG7OR 4G10", 30.0, 12.0),  # 30 m/h per posa, 12 conn/h
                ("FG7OR 4G16", 25.0, 10.0),  # 25 m/h per posa, 10 conn/h
                ("N07V-K 1G1.5", 40.0, 15.0),  # 40 m/h per posa, 15 conn/h
            ]
            
            updated_count = 0
            for codice, meters_per_hour, connections_per_hour in sample_productivity_data:
                cursor.execute("""
                    UPDATE tipologie_cavi 
                    SET base_productivity_meters_per_hour = %s,
                        base_productivity_connections_per_hour = %s
                    WHERE codice_prodotto ILIKE %s
                """, (meters_per_hour, connections_per_hour, f"%{codice}%"))
                
                if cursor.rowcount > 0:
                    updated_count += 1
                    print(f"✅ Aggiornata produttività per {codice}")
            
            if updated_count > 0:
                conn.commit()
                print(f"✅ Aggiornate {updated_count} tipologie con dati di produttività")
            else:
                print("⚠️ Nessuna tipologia aggiornata (potrebbero non esistere)")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ Errore durante l'inserimento dati: {str(e)}")
        return False

def main():
    """Funzione principale."""
    print("🚀 SETUP SISTEMA PRODUTTIVITÀ CAVI ELETTRICI")
    print("=" * 60)
    
    # Step 1: Aggiorna database
    if not update_database_for_productivity():
        print("❌ Aggiornamento database fallito")
        return False
    
    # Step 2: Testa le tabelle
    if not test_productivity_tables():
        print("❌ Test tabelle fallito")
        return False
    
    # Step 3: Inserisci dati di esempio
    if not insert_sample_productivity_data():
        print("⚠️ Inserimento dati di esempio fallito (non critico)")
    
    print("\n🎉 SETUP COMPLETATO CON SUCCESSO!")
    print("=" * 60)
    print("Il sistema di produttività è ora pronto per l'uso.")
    print("\n📚 DOCUMENTAZIONE API:")
    print("- POST /api/v1/work-logs - Crea nuovo work log")
    print("- GET /api/v1/work-logs - Lista work logs")
    print("- GET /api/v1/productivity/historical - Produttività storica")
    print("- POST /api/v1/predict/estimation - Stima predittiva")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
