#!/usr/bin/env python3
"""
Test API per comande usando requests
"""

import requests
import json
import sys

# Configurazione API
API_BASE = "http://localhost:8001"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_api_health():
    """Test di base per verificare che l'API sia attiva"""
    try:
        response = requests.get(f"{API_BASE}/health", timeout=5)
        print(f"✅ API Health: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ API non raggiungibile: {e}")
        return False

def test_get_cantieri():
    """Test per ottenere la lista dei cantieri"""
    try:
        response = requests.get(f"{API_BASE}/cantieri", headers=HEADERS, timeout=10)
        print(f"📋 Cantieri: {response.status_code}")
        
        if response.status_code == 200:
            cantieri = response.json()
            if cantieri:
                print(f"   Trovati {len(cantieri)} cantieri")
                return cantieri[0]['id_cantiere']
            else:
                print("   Nessun cantiere trovato")
                return None
        else:
            print(f"   Errore: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Errore nel recupero cantieri: {e}")
        return None

def test_get_cavi(id_cantiere):
    """Test per ottenere i cavi di un cantiere"""
    try:
        response = requests.get(f"{API_BASE}/cavi/cantiere/{id_cantiere}", headers=HEADERS, timeout=10)
        print(f"🔌 Cavi cantiere {id_cantiere}: {response.status_code}")
        
        if response.status_code == 200:
            cavi = response.json()
            if cavi:
                print(f"   Trovati {len(cavi)} cavi")
                # Trova cavi disponibili per POSA
                cavi_disponibili = [c for c in cavi if not c.get('comanda_posa') and c.get('metri_teorici', 0) > 0]
                print(f"   Cavi disponibili per POSA: {len(cavi_disponibili)}")
                return [c['id_cavo'] for c in cavi_disponibili[:2]]
            else:
                print("   Nessun cavo trovato")
                return []
        else:
            print(f"   Errore: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Errore nel recupero cavi: {e}")
        return []

def test_create_comanda_simple(id_cantiere):
    """Test creazione comanda semplice"""
    try:
        comanda_data = {
            "id_cantiere": id_cantiere,
            "tipo_comanda": "POSA",
            "descrizione": "Test comanda API",
            "responsabile": "Test User API"
        }
        
        response = requests.post(
            f"{API_BASE}/comande/", 
            headers=HEADERS, 
            json=comanda_data,
            timeout=30
        )
        
        print(f"🚀 Creazione comanda semplice: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"   ✅ Comanda creata: {result.get('codice_comanda')}")
            return result.get('codice_comanda')
        else:
            print(f"   ❌ Errore: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Errore nella creazione comanda: {e}")
        return None

def test_create_comanda_con_cavi(id_cantiere, lista_cavi):
    """Test creazione comanda con cavi"""
    try:
        comanda_data = {
            "tipo_comanda": "POSA",
            "descrizione": "Test comanda con cavi API",
            "responsabile": "Test User API",
            "numero_componenti_squadra": 1
        }
        
        # Costruisci URL con query parameters per i cavi
        cavi_params = "&".join([f"lista_id_cavi={cavo}" for cavo in lista_cavi])
        url = f"{API_BASE}/comande/cantiere/{id_cantiere}/crea-con-cavi?{cavi_params}"
        
        print(f"🔗 URL: {url}")
        print(f"📋 Data: {comanda_data}")
        
        response = requests.post(
            url,
            headers=HEADERS, 
            json=comanda_data,
            timeout=30
        )
        
        print(f"🚀 Creazione comanda con cavi: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"   ✅ Comanda creata: {result.get('codice_comanda')}")
            return result.get('codice_comanda')
        else:
            print(f"   ❌ Errore: {response.text}")
            try:
                error_detail = response.json()
                print(f"   Dettagli errore: {error_detail}")
            except:
                pass
            return None
    except Exception as e:
        print(f"❌ Errore nella creazione comanda con cavi: {e}")
        return None

def main():
    """Funzione principale"""
    print("🚀 Test API Comande")
    print("=" * 50)
    
    # Test 1: API Health
    if not test_api_health():
        print("❌ API non disponibile")
        return
    
    # Test 2: Cantieri
    id_cantiere = test_get_cantieri()
    if not id_cantiere:
        print("❌ Nessun cantiere disponibile")
        return
    
    # Test 3: Cavi
    lista_cavi = test_get_cavi(id_cantiere)
    
    # Test 4: Creazione comanda semplice
    comanda_semplice = test_create_comanda_simple(id_cantiere)
    
    # Test 5: Creazione comanda con cavi (se disponibili)
    if lista_cavi:
        comanda_con_cavi = test_create_comanda_con_cavi(id_cantiere, lista_cavi)
    else:
        print("⚠️ Nessun cavo disponibile per test comanda con cavi")
    
    print("\n" + "=" * 50)
    print("🏁 Test completati")

if __name__ == "__main__":
    main()
