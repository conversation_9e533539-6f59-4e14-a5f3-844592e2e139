{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\productivity\\\\EstimationTool.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axiosInstance from '../../services/axiosConfig';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EstimationTool = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    cable_type_id: '',\n    activity_type: 'Posa',\n    quantity_required: '',\n    environmental_conditions: 'Normale',\n    tools_used: 'Manuale',\n    number_of_operators: 1,\n    experience_level: 'Senior'\n  });\n  const [cableTypes, setCableTypes] = useState([]);\n  const [estimation, setEstimation] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Opzioni per i dropdown\n  const activityTypes = [{\n    value: 'Posa',\n    label: 'Posa'\n  }, {\n    value: 'Collegamento',\n    label: 'Collegamento'\n  }, {\n    value: 'Certificazione',\n    label: 'Certificazione'\n  }];\n  const environmentalConditions = [{\n    value: 'Normale',\n    label: 'Normale'\n  }, {\n    value: 'Spazi Ristretti',\n    label: 'Spazi Ristretti'\n  }, {\n    value: 'In Altezza',\n    label: 'In Altezza'\n  }, {\n    value: 'Esterno',\n    label: 'Esterno'\n  }];\n  const toolsUsed = [{\n    value: 'Manuale',\n    label: 'Manuale'\n  }, {\n    value: 'Automatico',\n    label: 'Automatico'\n  }];\n  const experienceLevels = [{\n    value: 'Apprentice',\n    label: 'Apprendista'\n  }, {\n    value: 'Junior',\n    label: 'Junior'\n  }, {\n    value: 'Senior',\n    label: 'Senior'\n  }];\n  useEffect(() => {\n    loadCableTypes();\n  }, []);\n  const loadCableTypes = async () => {\n    try {\n      const response = await axiosInstance.get('/admin/tipologie-cavi');\n      setCableTypes(response.data || []);\n    } catch (error) {\n      console.error('Errore nel caricamento tipi di cavo:', error);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || '' : value\n    }));\n\n    // Rimuovi errore per questo campo\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: null\n      }));\n    }\n\n    // Reset estimation quando cambiano i parametri\n    if (estimation) {\n      setEstimation(null);\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';\n    if (!formData.quantity_required || formData.quantity_required <= 0) {\n      newErrors.quantity_required = 'Quantità deve essere maggiore di 0';\n    }\n    if (!formData.number_of_operators || formData.number_of_operators < 1) {\n      newErrors.number_of_operators = 'Numero operatori deve essere almeno 1';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      setLoading(true);\n      setErrors({});\n\n      // Prepara i dati per l'invio\n      const submitData = {\n        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,\n        activity_type: formData.activity_type,\n        quantity_required: parseFloat(formData.quantity_required),\n        environmental_conditions: formData.environmental_conditions,\n        tools_used: formData.tools_used,\n        number_of_operators: parseInt(formData.number_of_operators),\n        experience_level: formData.experience_level\n      };\n      const response = await axiosInstance.post('/v1/predict/estimation', submitData);\n      setEstimation(response.data);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore nella stima:', error);\n      setErrors({\n        general: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore nel calcolo della stima'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatHours = hours => {\n    if (hours < 1) {\n      return `${Math.round(hours * 60)} minuti`;\n    } else if (hours < 24) {\n      const h = Math.floor(hours);\n      const m = Math.round((hours - h) * 60);\n      return m > 0 ? `${h}h ${m}m` : `${h}h`;\n    } else {\n      const days = Math.floor(hours / 24);\n      const h = Math.round(hours % 24);\n      return h > 0 ? `${days}g ${h}h` : `${days}g`;\n    }\n  };\n  const getCorrectionFactorColor = factor => {\n    if (factor > 1) return 'text-green-600';\n    if (factor < 1) return 'text-red-600';\n    return 'text-gray-600';\n  };\n  const getCorrectionFactorIcon = factor => {\n    if (factor > 1) return '↗️';\n    if (factor < 1) return '↘️';\n    return '➡️';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white p-6 rounded-lg shadow-lg max-w-6xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold text-gray-800 mb-6\",\n      children: \"\\uD83D\\uDD2E Strumento di Stima Produttivit\\xE0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-700 mb-4\",\n          children: \"Parametri del Lavoro\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), errors.general && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n          children: errors.general\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Tipo Attivit\\xE0 *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"activity_type\",\n              value: formData.activity_type,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.activity_type ? 'border-red-500' : 'border-gray-300'}`,\n              children: activityTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: type.value,\n                children: type.label\n              }, type.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), errors.activity_type && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-sm mt-1\",\n              children: errors.activity_type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 40\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Tipo di Cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"cable_type_id\",\n              value: formData.cable_type_id,\n              onChange: handleInputChange,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Seleziona tipo di cavo (opzionale)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), cableTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: type.id_tipologia,\n                children: [type.codice_prodotto, \" - \", type.nome_commerciale]\n              }, type.id_tipologia, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: [\"Quantit\\xE0 Richiesta * \", formData.activity_type === 'Posa' ? '(metri)' : '(unità)']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"quantity_required\",\n              value: formData.quantity_required,\n              onChange: handleInputChange,\n              step: \"0.1\",\n              min: \"0\",\n              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.quantity_required ? 'border-red-500' : 'border-gray-300'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), errors.quantity_required && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-sm mt-1\",\n              children: errors.quantity_required\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 44\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Condizioni Ambientali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"environmental_conditions\",\n              value: formData.environmental_conditions,\n              onChange: handleInputChange,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: environmentalConditions.map(condition => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: condition.value,\n                children: condition.label\n              }, condition.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Strumenti Utilizzati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"tools_used\",\n              value: formData.tools_used,\n              onChange: handleInputChange,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: toolsUsed.map(tool => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: tool.value,\n                children: tool.label\n              }, tool.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Numero Operatori *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"number_of_operators\",\n              value: formData.number_of_operators,\n              onChange: handleInputChange,\n              min: \"1\",\n              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.number_of_operators ? 'border-red-500' : 'border-gray-300'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), errors.number_of_operators && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-sm mt-1\",\n              children: errors.number_of_operators\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 46\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Livello di Esperienza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"experience_level\",\n              value: formData.experience_level,\n              onChange: handleInputChange,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: experienceLevels.map(level => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: level.value,\n                children: level.label\n              }, level.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"w-full px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 font-medium\",\n            children: loading ? 'Calcolando...' : '🔮 Calcola Stima'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-700 mb-4\",\n          children: \"Risultati della Stima\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), estimation ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-800 mb-2\",\n              children: \"\\uD83D\\uDCCB Riepilogo Input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Attivit\\xE0:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 22\n                }, this), \" \", estimation.inputs.activity_type]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Quantit\\xE0:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 22\n                }, this), \" \", estimation.inputs.quantity_required, \" \", estimation.inputs.activity_type === 'Posa' ? 'metri' : 'unità']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Operatori:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 22\n                }, this), \" \", estimation.inputs.number_of_operators]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Condizioni:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 22\n                }, this), \" \", estimation.inputs.environmental_conditions]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Strumenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 22\n                }, this), \" \", estimation.inputs.tools_used]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Esperienza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 22\n                }, this), \" \", estimation.inputs.experience_level]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-blue-800 mb-3\",\n              children: \"\\uD83C\\uDFAF Risultati Principali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-700\",\n                  children: \"Tempo stimato per il team:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-bold text-blue-900\",\n                  children: formatHours(estimation.estimated_time_for_team_hours)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-700\",\n                  children: \"Ore-uomo totali:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-bold text-blue-900\",\n                  children: [estimation.estimated_total_man_hours.toFixed(1), \"h\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-green-800 mb-3\",\n              children: \"\\uD83D\\uDCCA Dettagli Produttivit\\xE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-700\",\n                  children: \"Produttivit\\xE0 base:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-green-900\",\n                  children: [estimation.base_productivity.toFixed(1), \" \", estimation.inputs.activity_type === 'Posa' ? 'm/h' : 'unità/h']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-700\",\n                  children: \"Produttivit\\xE0 attesa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-green-900\",\n                  children: [estimation.expected_productivity_per_operator.toFixed(1), \" \", estimation.inputs.activity_type === 'Posa' ? 'm/h' : 'unità/h', \"/operatore\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-yellow-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-yellow-800 mb-3\",\n              children: \"\\u2699\\uFE0F Fattori di Correzione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: Object.entries(estimation.correction_factors).map(([factor, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-yellow-700 capitalize\",\n                  children: [factor.replace('_', ' '), \":\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `font-medium ${getCorrectionFactorColor(value)}`,\n                  children: [getCorrectionFactorIcon(value), \" \", (value * 100).toFixed(0), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 23\n                }, this)]\n              }, factor, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-800 mb-2\",\n              children: \"\\uD83D\\uDCA1 Note\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 Le stime sono basate su dati storici e fattori di correzione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 I tempi effettivi possono variare in base a condizioni specifiche\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 Utilizzare come riferimento per la pianificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-gray-500 py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-6xl mb-4\",\n            children: \"\\uD83D\\uDD2E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Inserisci i parametri e clicca \\\"Calcola Stima\\\" per vedere i risultati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n};\n_s(EstimationTool, \"Fdfa3V1PCa+zRqPC5ZEYYrQiFCA=\");\n_c = EstimationTool;\nexport default EstimationTool;\nvar _c;\n$RefreshReg$(_c, \"EstimationTool\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axiosInstance", "jsxDEV", "_jsxDEV", "EstimationTool", "_s", "formData", "setFormData", "cable_type_id", "activity_type", "quantity_required", "environmental_conditions", "tools_used", "number_of_operators", "experience_level", "cableTypes", "setCableTypes", "estimation", "setEstimation", "loading", "setLoading", "errors", "setErrors", "activityTypes", "value", "label", "environmentalConditions", "toolsUsed", "experienceLevels", "loadCableTypes", "response", "get", "data", "error", "console", "handleInputChange", "e", "name", "type", "target", "prev", "parseFloat", "validateForm", "newErrors", "Object", "keys", "length", "handleSubmit", "preventDefault", "submitData", "parseInt", "post", "_error$response", "_error$response$data", "general", "detail", "formatHours", "hours", "Math", "round", "h", "floor", "m", "days", "getCorrectionFactorColor", "factor", "getCorrectionFactorIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "onChange", "map", "id_tipologia", "codice_prodotto", "nome_commerciale", "step", "min", "condition", "tool", "level", "disabled", "inputs", "estimated_time_for_team_hours", "estimated_total_man_hours", "toFixed", "base_productivity", "expected_productivity_per_operator", "entries", "correction_factors", "replace", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/productivity/EstimationTool.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axiosInstance from '../../services/axiosConfig';\n\nconst EstimationTool = () => {\n  const [formData, setFormData] = useState({\n    cable_type_id: '',\n    activity_type: 'Posa',\n    quantity_required: '',\n    environmental_conditions: 'Normale',\n    tools_used: 'Manuale',\n    number_of_operators: 1,\n    experience_level: 'Senior'\n  });\n\n  const [cableTypes, setCableTypes] = useState([]);\n  const [estimation, setEstimation] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Opzioni per i dropdown\n  const activityTypes = [\n    { value: 'Posa', label: 'Posa' },\n    { value: 'Collegamento', label: 'Collegamento' },\n    { value: 'Certificazione', label: 'Certificazione' }\n  ];\n\n  const environmentalConditions = [\n    { value: 'Normale', label: 'Normale' },\n    { value: '<PERSON><PERSON>', label: '<PERSON><PERSON>' },\n    { value: 'In Altezza', label: 'In Altezza' },\n    { value: 'Esterno', label: 'Esterno' }\n  ];\n\n  const toolsUsed = [\n    { value: 'Manuale', label: 'Manuale' },\n    { value: 'Automatico', label: 'Automatico' }\n  ];\n\n  const experienceLevels = [\n    { value: 'Apprentice', label: 'Apprendista' },\n    { value: 'Junior', label: 'Junior' },\n    { value: 'Senior', label: 'Senior' }\n  ];\n\n  useEffect(() => {\n    loadCableTypes();\n  }, []);\n\n  const loadCableTypes = async () => {\n    try {\n      const response = await axiosInstance.get('/admin/tipologie-cavi');\n      setCableTypes(response.data || []);\n    } catch (error) {\n      console.error('Errore nel caricamento tipi di cavo:', error);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'number' ? parseFloat(value) || '' : value\n    }));\n    \n    // Rimuovi errore per questo campo\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: null }));\n    }\n\n    // Reset estimation quando cambiano i parametri\n    if (estimation) {\n      setEstimation(null);\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';\n    if (!formData.quantity_required || formData.quantity_required <= 0) {\n      newErrors.quantity_required = 'Quantità deve essere maggiore di 0';\n    }\n    if (!formData.number_of_operators || formData.number_of_operators < 1) {\n      newErrors.number_of_operators = 'Numero operatori deve essere almeno 1';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setErrors({});\n      \n      // Prepara i dati per l'invio\n      const submitData = {\n        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,\n        activity_type: formData.activity_type,\n        quantity_required: parseFloat(formData.quantity_required),\n        environmental_conditions: formData.environmental_conditions,\n        tools_used: formData.tools_used,\n        number_of_operators: parseInt(formData.number_of_operators),\n        experience_level: formData.experience_level\n      };\n\n      const response = await axiosInstance.post('/v1/predict/estimation', submitData);\n      setEstimation(response.data);\n      \n    } catch (error) {\n      console.error('Errore nella stima:', error);\n      setErrors({ \n        general: error.response?.data?.detail || 'Errore nel calcolo della stima' \n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatHours = (hours) => {\n    if (hours < 1) {\n      return `${Math.round(hours * 60)} minuti`;\n    } else if (hours < 24) {\n      const h = Math.floor(hours);\n      const m = Math.round((hours - h) * 60);\n      return m > 0 ? `${h}h ${m}m` : `${h}h`;\n    } else {\n      const days = Math.floor(hours / 24);\n      const h = Math.round(hours % 24);\n      return h > 0 ? `${days}g ${h}h` : `${days}g`;\n    }\n  };\n\n  const getCorrectionFactorColor = (factor) => {\n    if (factor > 1) return 'text-green-600';\n    if (factor < 1) return 'text-red-600';\n    return 'text-gray-600';\n  };\n\n  const getCorrectionFactorIcon = (factor) => {\n    if (factor > 1) return '↗️';\n    if (factor < 1) return '↘️';\n    return '➡️';\n  };\n\n  return (\n    <div className=\"bg-white p-6 rounded-lg shadow-lg max-w-6xl mx-auto\">\n      <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">\n        🔮 Strumento di Stima Produttività\n      </h2>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Form di Input */}\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-700 mb-4\">Parametri del Lavoro</h3>\n          \n          {errors.general && (\n            <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n              {errors.general}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {/* Tipo di Attività */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Tipo Attività *\n              </label>\n              <select\n                name=\"activity_type\"\n                value={formData.activity_type}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                  errors.activity_type ? 'border-red-500' : 'border-gray-300'\n                }`}\n              >\n                {activityTypes.map(type => (\n                  <option key={type.value} value={type.value}>\n                    {type.label}\n                  </option>\n                ))}\n              </select>\n              {errors.activity_type && <p className=\"text-red-500 text-sm mt-1\">{errors.activity_type}</p>}\n            </div>\n\n            {/* Tipo di Cavo */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Tipo di Cavo\n              </label>\n              <select\n                name=\"cable_type_id\"\n                value={formData.cable_type_id}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">Seleziona tipo di cavo (opzionale)</option>\n                {cableTypes.map(type => (\n                  <option key={type.id_tipologia} value={type.id_tipologia}>\n                    {type.codice_prodotto} - {type.nome_commerciale}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Quantità Richiesta */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Quantità Richiesta * {formData.activity_type === 'Posa' ? '(metri)' : '(unità)'}\n              </label>\n              <input\n                type=\"number\"\n                name=\"quantity_required\"\n                value={formData.quantity_required}\n                onChange={handleInputChange}\n                step=\"0.1\"\n                min=\"0\"\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                  errors.quantity_required ? 'border-red-500' : 'border-gray-300'\n                }`}\n              />\n              {errors.quantity_required && <p className=\"text-red-500 text-sm mt-1\">{errors.quantity_required}</p>}\n            </div>\n\n            {/* Condizioni Ambientali */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Condizioni Ambientali\n              </label>\n              <select\n                name=\"environmental_conditions\"\n                value={formData.environmental_conditions}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                {environmentalConditions.map(condition => (\n                  <option key={condition.value} value={condition.value}>\n                    {condition.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Strumenti Utilizzati */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Strumenti Utilizzati\n              </label>\n              <select\n                name=\"tools_used\"\n                value={formData.tools_used}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                {toolsUsed.map(tool => (\n                  <option key={tool.value} value={tool.value}>\n                    {tool.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Numero Operatori */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Numero Operatori *\n              </label>\n              <input\n                type=\"number\"\n                name=\"number_of_operators\"\n                value={formData.number_of_operators}\n                onChange={handleInputChange}\n                min=\"1\"\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${\n                  errors.number_of_operators ? 'border-red-500' : 'border-gray-300'\n                }`}\n              />\n              {errors.number_of_operators && <p className=\"text-red-500 text-sm mt-1\">{errors.number_of_operators}</p>}\n            </div>\n\n            {/* Livello di Esperienza */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Livello di Esperienza\n              </label>\n              <select\n                name=\"experience_level\"\n                value={formData.experience_level}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                {experienceLevels.map(level => (\n                  <option key={level.value} value={level.value}>\n                    {level.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 font-medium\"\n            >\n              {loading ? 'Calcolando...' : '🔮 Calcola Stima'}\n            </button>\n          </form>\n        </div>\n\n        {/* Risultati della Stima */}\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-700 mb-4\">Risultati della Stima</h3>\n          \n          {estimation ? (\n            <div className=\"space-y-4\">\n              {/* Riepilogo Input */}\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">📋 Riepilogo Input</h4>\n                <div className=\"text-sm text-gray-600 space-y-1\">\n                  <p><strong>Attività:</strong> {estimation.inputs.activity_type}</p>\n                  <p><strong>Quantità:</strong> {estimation.inputs.quantity_required} {estimation.inputs.activity_type === 'Posa' ? 'metri' : 'unità'}</p>\n                  <p><strong>Operatori:</strong> {estimation.inputs.number_of_operators}</p>\n                  <p><strong>Condizioni:</strong> {estimation.inputs.environmental_conditions}</p>\n                  <p><strong>Strumenti:</strong> {estimation.inputs.tools_used}</p>\n                  <p><strong>Esperienza:</strong> {estimation.inputs.experience_level}</p>\n                </div>\n              </div>\n\n              {/* Risultati Principali */}\n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <h4 className=\"font-medium text-blue-800 mb-3\">🎯 Risultati Principali</h4>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-blue-700\">Tempo stimato per il team:</span>\n                    <span className=\"font-bold text-blue-900\">\n                      {formatHours(estimation.estimated_time_for_team_hours)}\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-blue-700\">Ore-uomo totali:</span>\n                    <span className=\"font-bold text-blue-900\">\n                      {estimation.estimated_total_man_hours.toFixed(1)}h\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Dettagli Produttività */}\n              <div className=\"bg-green-50 p-4 rounded-lg\">\n                <h4 className=\"font-medium text-green-800 mb-3\">📊 Dettagli Produttività</h4>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-green-700\">Produttività base:</span>\n                    <span className=\"font-medium text-green-900\">\n                      {estimation.base_productivity.toFixed(1)} {estimation.inputs.activity_type === 'Posa' ? 'm/h' : 'unità/h'}\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-green-700\">Produttività attesa:</span>\n                    <span className=\"font-medium text-green-900\">\n                      {estimation.expected_productivity_per_operator.toFixed(1)} {estimation.inputs.activity_type === 'Posa' ? 'm/h' : 'unità/h'}/operatore\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Fattori di Correzione */}\n              <div className=\"bg-yellow-50 p-4 rounded-lg\">\n                <h4 className=\"font-medium text-yellow-800 mb-3\">⚙️ Fattori di Correzione</h4>\n                <div className=\"space-y-2\">\n                  {Object.entries(estimation.correction_factors).map(([factor, value]) => (\n                    <div key={factor} className=\"flex justify-between items-center\">\n                      <span className=\"text-yellow-700 capitalize\">\n                        {factor.replace('_', ' ')}:\n                      </span>\n                      <span className={`font-medium ${getCorrectionFactorColor(value)}`}>\n                        {getCorrectionFactorIcon(value)} {(value * 100).toFixed(0)}%\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Note */}\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">💡 Note</h4>\n                <div className=\"text-sm text-gray-600 space-y-1\">\n                  <p>• Le stime sono basate su dati storici e fattori di correzione</p>\n                  <p>• I tempi effettivi possono variare in base a condizioni specifiche</p>\n                  <p>• Utilizzare come riferimento per la pianificazione</p>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center text-gray-500 py-12\">\n              <div className=\"text-6xl mb-4\">🔮</div>\n              <p>Inserisci i parametri e clicca \"Calcola Stima\" per vedere i risultati</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EstimationTool;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC;IACvCS,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,MAAM;IACrBC,iBAAiB,EAAE,EAAE;IACrBC,wBAAwB,EAAE,SAAS;IACnCC,UAAU,EAAE,SAAS;IACrBC,mBAAmB,EAAE,CAAC;IACtBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAMwB,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChC;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC,EAChD;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAiB,CAAC,CACrD;EAED,MAAMC,uBAAuB,GAAG,CAC9B;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,CACvC;EAED,MAAME,SAAS,GAAG,CAChB;IAAEH,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,CAC7C;EAED,MAAMG,gBAAgB,GAAG,CACvB;IAAEJ,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC7C;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,CACrC;EAEDzB,SAAS,CAAC,MAAM;IACd6B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM7B,aAAa,CAAC8B,GAAG,CAAC,uBAAuB,CAAC;MACjEf,aAAa,CAACc,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D;EACF,CAAC;EAED,MAAME,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEb,KAAK;MAAEc;IAAK,CAAC,GAAGF,CAAC,CAACG,MAAM;IACtChC,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC,IAAI,KAAK,QAAQ,GAAGG,UAAU,CAACjB,KAAK,CAAC,IAAI,EAAE,GAAGA;IACxD,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIH,MAAM,CAACgB,IAAI,CAAC,EAAE;MAChBf,SAAS,CAACkB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAG;MAAK,CAAC,CAAC,CAAC;IAChD;;IAEA;IACA,IAAIpB,UAAU,EAAE;MACdC,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMwB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACrC,QAAQ,CAACG,aAAa,EAAEkC,SAAS,CAAClC,aAAa,GAAG,yBAAyB;IAChF,IAAI,CAACH,QAAQ,CAACI,iBAAiB,IAAIJ,QAAQ,CAACI,iBAAiB,IAAI,CAAC,EAAE;MAClEiC,SAAS,CAACjC,iBAAiB,GAAG,oCAAoC;IACpE;IACA,IAAI,CAACJ,QAAQ,CAACO,mBAAmB,IAAIP,QAAQ,CAACO,mBAAmB,GAAG,CAAC,EAAE;MACrE8B,SAAS,CAAC9B,mBAAmB,GAAG,uCAAuC;IACzE;IAEAS,SAAS,CAACqB,SAAS,CAAC;IACpB,OAAOC,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAACG,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChBE,SAAS,CAAC,CAAC,CAAC,CAAC;;MAEb;MACA,MAAM2B,UAAU,GAAG;QACjBzC,aAAa,EAAEF,QAAQ,CAACE,aAAa,GAAG0C,QAAQ,CAAC5C,QAAQ,CAACE,aAAa,CAAC,GAAG,IAAI;QAC/EC,aAAa,EAAEH,QAAQ,CAACG,aAAa;QACrCC,iBAAiB,EAAE+B,UAAU,CAACnC,QAAQ,CAACI,iBAAiB,CAAC;QACzDC,wBAAwB,EAAEL,QAAQ,CAACK,wBAAwB;QAC3DC,UAAU,EAAEN,QAAQ,CAACM,UAAU;QAC/BC,mBAAmB,EAAEqC,QAAQ,CAAC5C,QAAQ,CAACO,mBAAmB,CAAC;QAC3DC,gBAAgB,EAAER,QAAQ,CAACQ;MAC7B,CAAC;MAED,MAAMgB,QAAQ,GAAG,MAAM7B,aAAa,CAACkD,IAAI,CAAC,wBAAwB,EAAEF,UAAU,CAAC;MAC/E/B,aAAa,CAACY,QAAQ,CAACE,IAAI,CAAC;IAE9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACdnB,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CX,SAAS,CAAC;QACRgC,OAAO,EAAE,EAAAF,eAAA,GAAAnB,KAAK,CAACH,QAAQ,cAAAsB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBpB,IAAI,cAAAqB,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAI;MAC3C,CAAC,CAAC;IACJ,CAAC,SAAS;MACRnC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,WAAW,GAAIC,KAAK,IAAK;IAC7B,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAG,EAAE,CAAC,SAAS;IAC3C,CAAC,MAAM,IAAIA,KAAK,GAAG,EAAE,EAAE;MACrB,MAAMG,CAAC,GAAGF,IAAI,CAACG,KAAK,CAACJ,KAAK,CAAC;MAC3B,MAAMK,CAAC,GAAGJ,IAAI,CAACC,KAAK,CAAC,CAACF,KAAK,GAAGG,CAAC,IAAI,EAAE,CAAC;MACtC,OAAOE,CAAC,GAAG,CAAC,GAAG,GAAGF,CAAC,KAAKE,CAAC,GAAG,GAAG,GAAGF,CAAC,GAAG;IACxC,CAAC,MAAM;MACL,MAAMG,IAAI,GAAGL,IAAI,CAACG,KAAK,CAACJ,KAAK,GAAG,EAAE,CAAC;MACnC,MAAMG,CAAC,GAAGF,IAAI,CAACC,KAAK,CAACF,KAAK,GAAG,EAAE,CAAC;MAChC,OAAOG,CAAC,GAAG,CAAC,GAAG,GAAGG,IAAI,KAAKH,CAAC,GAAG,GAAG,GAAGG,IAAI,GAAG;IAC9C;EACF,CAAC;EAED,MAAMC,wBAAwB,GAAIC,MAAM,IAAK;IAC3C,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO,gBAAgB;IACvC,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO,cAAc;IACrC,OAAO,eAAe;EACxB,CAAC;EAED,MAAMC,uBAAuB,GAAID,MAAM,IAAK;IAC1C,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IAC3B,IAAIA,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IAC3B,OAAO,IAAI;EACb,CAAC;EAED,oBACE9D,OAAA;IAAKgE,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAClEjE,OAAA;MAAIgE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAEtD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELrE,OAAA;MAAKgE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDjE,OAAA;QAAAiE,QAAA,gBACEjE,OAAA;UAAIgE,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEjFnD,MAAM,CAACiC,OAAO,iBACbnD,OAAA;UAAKgE,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EAClF/C,MAAM,CAACiC;QAAO;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CACN,eAEDrE,OAAA;UAAMsE,QAAQ,EAAE1B,YAAa;UAACoB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAEjDjE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAOgE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrE,OAAA;cACEkC,IAAI,EAAC,eAAe;cACpBb,KAAK,EAAElB,QAAQ,CAACG,aAAc;cAC9BiE,QAAQ,EAAEvC,iBAAkB;cAC5BgC,SAAS,EAAE,0FACT9C,MAAM,CAACZ,aAAa,GAAG,gBAAgB,GAAG,iBAAiB,EAC1D;cAAA2D,QAAA,EAEF7C,aAAa,CAACoD,GAAG,CAACrC,IAAI,iBACrBnC,OAAA;gBAAyBqB,KAAK,EAAEc,IAAI,CAACd,KAAM;gBAAA4C,QAAA,EACxC9B,IAAI,CAACb;cAAK,GADAa,IAAI,CAACd,KAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACRnD,MAAM,CAACZ,aAAa,iBAAIN,OAAA;cAAGgE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE/C,MAAM,CAACZ;YAAa;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eAGNrE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAOgE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrE,OAAA;cACEkC,IAAI,EAAC,eAAe;cACpBb,KAAK,EAAElB,QAAQ,CAACE,aAAc;cAC9BkE,QAAQ,EAAEvC,iBAAkB;cAC5BgC,SAAS,EAAC,wGAAwG;cAAAC,QAAA,gBAElHjE,OAAA;gBAAQqB,KAAK,EAAC,EAAE;gBAAA4C,QAAA,EAAC;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC3DzD,UAAU,CAAC4D,GAAG,CAACrC,IAAI,iBAClBnC,OAAA;gBAAgCqB,KAAK,EAAEc,IAAI,CAACsC,YAAa;gBAAAR,QAAA,GACtD9B,IAAI,CAACuC,eAAe,EAAC,KAAG,EAACvC,IAAI,CAACwC,gBAAgB;cAAA,GADpCxC,IAAI,CAACsC,YAAY;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEtB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNrE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAOgE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAC,0BACzC,EAAC9D,QAAQ,CAACG,aAAa,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACRrE,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACbD,IAAI,EAAC,mBAAmB;cACxBb,KAAK,EAAElB,QAAQ,CAACI,iBAAkB;cAClCgE,QAAQ,EAAEvC,iBAAkB;cAC5B4C,IAAI,EAAC,KAAK;cACVC,GAAG,EAAC,GAAG;cACPb,SAAS,EAAE,0FACT9C,MAAM,CAACX,iBAAiB,GAAG,gBAAgB,GAAG,iBAAiB;YAC9D;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACDnD,MAAM,CAACX,iBAAiB,iBAAIP,OAAA;cAAGgE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE/C,MAAM,CAACX;YAAiB;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG,CAAC,eAGNrE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAOgE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrE,OAAA;cACEkC,IAAI,EAAC,0BAA0B;cAC/Bb,KAAK,EAAElB,QAAQ,CAACK,wBAAyB;cACzC+D,QAAQ,EAAEvC,iBAAkB;cAC5BgC,SAAS,EAAC,wGAAwG;cAAAC,QAAA,EAEjH1C,uBAAuB,CAACiD,GAAG,CAACM,SAAS,iBACpC9E,OAAA;gBAA8BqB,KAAK,EAAEyD,SAAS,CAACzD,KAAM;gBAAA4C,QAAA,EAClDa,SAAS,CAACxD;cAAK,GADLwD,SAAS,CAACzD,KAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEpB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNrE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAOgE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrE,OAAA;cACEkC,IAAI,EAAC,YAAY;cACjBb,KAAK,EAAElB,QAAQ,CAACM,UAAW;cAC3B8D,QAAQ,EAAEvC,iBAAkB;cAC5BgC,SAAS,EAAC,wGAAwG;cAAAC,QAAA,EAEjHzC,SAAS,CAACgD,GAAG,CAACO,IAAI,iBACjB/E,OAAA;gBAAyBqB,KAAK,EAAE0D,IAAI,CAAC1D,KAAM;gBAAA4C,QAAA,EACxCc,IAAI,CAACzD;cAAK,GADAyD,IAAI,CAAC1D,KAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNrE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAOgE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrE,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACbD,IAAI,EAAC,qBAAqB;cAC1Bb,KAAK,EAAElB,QAAQ,CAACO,mBAAoB;cACpC6D,QAAQ,EAAEvC,iBAAkB;cAC5B6C,GAAG,EAAC,GAAG;cACPb,SAAS,EAAE,0FACT9C,MAAM,CAACR,mBAAmB,GAAG,gBAAgB,GAAG,iBAAiB;YAChE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACDnD,MAAM,CAACR,mBAAmB,iBAAIV,OAAA;cAAGgE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE/C,MAAM,CAACR;YAAmB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,eAGNrE,OAAA;YAAAiE,QAAA,gBACEjE,OAAA;cAAOgE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrE,OAAA;cACEkC,IAAI,EAAC,kBAAkB;cACvBb,KAAK,EAAElB,QAAQ,CAACQ,gBAAiB;cACjC4D,QAAQ,EAAEvC,iBAAkB;cAC5BgC,SAAS,EAAC,wGAAwG;cAAAC,QAAA,EAEjHxC,gBAAgB,CAAC+C,GAAG,CAACQ,KAAK,iBACzBhF,OAAA;gBAA0BqB,KAAK,EAAE2D,KAAK,CAAC3D,KAAM;gBAAA4C,QAAA,EAC1Ce,KAAK,CAAC1D;cAAK,GADD0D,KAAK,CAAC3D,KAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENrE,OAAA;YACEmC,IAAI,EAAC,QAAQ;YACb8C,QAAQ,EAAEjE,OAAQ;YAClBgD,SAAS,EAAC,0JAA0J;YAAAC,QAAA,EAEnKjD,OAAO,GAAG,eAAe,GAAG;UAAkB;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNrE,OAAA;QAAAiE,QAAA,gBACEjE,OAAA;UAAIgE,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAElFvD,UAAU,gBACTd,OAAA;UAAKgE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBjE,OAAA;YAAKgE,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCjE,OAAA;cAAIgE,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtErE,OAAA;cAAKgE,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CjE,OAAA;gBAAAiE,QAAA,gBAAGjE,OAAA;kBAAAiE,QAAA,EAAQ;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvD,UAAU,CAACoE,MAAM,CAAC5E,aAAa;cAAA;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnErE,OAAA;gBAAAiE,QAAA,gBAAGjE,OAAA;kBAAAiE,QAAA,EAAQ;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvD,UAAU,CAACoE,MAAM,CAAC3E,iBAAiB,EAAC,GAAC,EAACO,UAAU,CAACoE,MAAM,CAAC5E,aAAa,KAAK,MAAM,GAAG,OAAO,GAAG,OAAO;cAAA;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxIrE,OAAA;gBAAAiE,QAAA,gBAAGjE,OAAA;kBAAAiE,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvD,UAAU,CAACoE,MAAM,CAACxE,mBAAmB;cAAA;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1ErE,OAAA;gBAAAiE,QAAA,gBAAGjE,OAAA;kBAAAiE,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvD,UAAU,CAACoE,MAAM,CAAC1E,wBAAwB;cAAA;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFrE,OAAA;gBAAAiE,QAAA,gBAAGjE,OAAA;kBAAAiE,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvD,UAAU,CAACoE,MAAM,CAACzE,UAAU;cAAA;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjErE,OAAA;gBAAAiE,QAAA,gBAAGjE,OAAA;kBAAAiE,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvD,UAAU,CAACoE,MAAM,CAACvE,gBAAgB;cAAA;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrE,OAAA;YAAKgE,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCjE,OAAA;cAAIgE,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3ErE,OAAA;cAAKgE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjE,OAAA;gBAAKgE,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnCjE,OAAA;kBAAMgE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjErE,OAAA;kBAAMgE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EACtCZ,WAAW,CAACvC,UAAU,CAACqE,6BAA6B;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrE,OAAA;gBAAKgE,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnCjE,OAAA;kBAAMgE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvDrE,OAAA;kBAAMgE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,GACtCnD,UAAU,CAACsE,yBAAyB,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GACnD;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrE,OAAA;YAAKgE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCjE,OAAA;cAAIgE,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7ErE,OAAA;cAAKgE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBjE,OAAA;gBAAKgE,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnCjE,OAAA;kBAAMgE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DrE,OAAA;kBAAMgE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACzCnD,UAAU,CAACwE,iBAAiB,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,EAACvE,UAAU,CAACoE,MAAM,CAAC5E,aAAa,KAAK,MAAM,GAAG,KAAK,GAAG,SAAS;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNrE,OAAA;gBAAKgE,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnCjE,OAAA;kBAAMgE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5DrE,OAAA;kBAAMgE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACzCnD,UAAU,CAACyE,kCAAkC,CAACF,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,EAACvE,UAAU,CAACoE,MAAM,CAAC5E,aAAa,KAAK,MAAM,GAAG,KAAK,GAAG,SAAS,EAAC,YAC7H;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrE,OAAA;YAAKgE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CjE,OAAA;cAAIgE,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ErE,OAAA;cAAKgE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBxB,MAAM,CAAC+C,OAAO,CAAC1E,UAAU,CAAC2E,kBAAkB,CAAC,CAACjB,GAAG,CAAC,CAAC,CAACV,MAAM,EAAEzC,KAAK,CAAC,kBACjErB,OAAA;gBAAkBgE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAC7DjE,OAAA;kBAAMgE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACzCH,MAAM,CAAC4B,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAC,GAC5B;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPrE,OAAA;kBAAMgE,SAAS,EAAE,eAAeH,wBAAwB,CAACxC,KAAK,CAAC,EAAG;kBAAA4C,QAAA,GAC/DF,uBAAuB,CAAC1C,KAAK,CAAC,EAAC,GAAC,EAAC,CAACA,KAAK,GAAG,GAAG,EAAEgE,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7D;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GANCP,MAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOX,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrE,OAAA;YAAKgE,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCjE,OAAA;cAAIgE,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DrE,OAAA;cAAKgE,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CjE,OAAA;gBAAAiE,QAAA,EAAG;cAA8D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrErE,OAAA;gBAAAiE,QAAA,EAAG;cAAmE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1ErE,OAAA;gBAAAiE,QAAA,EAAG;cAAmD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENrE,OAAA;UAAKgE,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CjE,OAAA;YAAKgE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCrE,OAAA;YAAAiE,QAAA,EAAG;UAAqE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CAtZID,cAAc;AAAA0F,EAAA,GAAd1F,cAAc;AAwZpB,eAAeA,cAAc;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}