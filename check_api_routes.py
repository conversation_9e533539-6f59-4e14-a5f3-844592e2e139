#!/usr/bin/env python3
"""
Script per verificare le route API disponibili.
"""

import requests
import json

def check_api_routes():
    """Verifica le route API disponibili."""
    try:
        print("🔍 VERIFICA ROUTE API DISPONIBILI")
        print("=" * 50)
        
        # Controlla l'endpoint di documentazione OpenAPI
        response = requests.get("http://localhost:8001/openapi.json", timeout=10)
        
        if response.status_code == 200:
            openapi_spec = response.json()
            paths = openapi_spec.get('paths', {})
            
            print(f"✅ Trovate {len(paths)} route API:")
            
            # Filtra le route che contengono 'work-logs' o 'productivity'
            productivity_routes = []
            for path, methods in paths.items():
                if 'work-logs' in path or 'productivity' in path or 'predict' in path:
                    productivity_routes.append(path)
                    for method in methods.keys():
                        print(f"   {method.upper()} {path}")
            
            if productivity_routes:
                print(f"\n✅ Trovate {len(productivity_routes)} route di produttività")
            else:
                print("\n❌ Nessuna route di produttività trovata")
                print("   Il router potrebbe non essere stato caricato")
            
            # Mostra alcune route principali per confronto
            print(f"\n📋 Alcune route principali:")
            main_routes = [path for path in paths.keys() if any(x in path for x in ['/auth/', '/cantieri/', '/cavi/'])]
            for route in main_routes[:5]:
                print(f"   {route}")
            
            return len(productivity_routes) > 0
            
        else:
            print(f"❌ Errore nel recupero OpenAPI spec: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Errore: {str(e)}")
        return False

def test_simple_endpoint():
    """Testa un endpoint semplice per verificare che l'API funzioni."""
    try:
        print("\n🧪 TEST ENDPOINT SEMPLICE")
        print("=" * 30)
        
        response = requests.get("http://localhost:8001/api/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check OK: {data.get('status')}")
            print(f"   Database: {data.get('database')}")
            return True
        else:
            print(f"❌ Health check fallito: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Errore: {str(e)}")
        return False

def main():
    """Funzione principale."""
    print("🔍 DIAGNOSI ROUTE API")
    print("=" * 60)
    
    # Test 1: Verifica che l'API sia raggiungibile
    if not test_simple_endpoint():
        print("\n❌ API non raggiungibile")
        return False
    
    # Test 2: Verifica le route disponibili
    if not check_api_routes():
        print("\n⚠️ Route di produttività non trovate")
        print("\n💡 POSSIBILI SOLUZIONI:")
        print("1. Riavviare il backend per caricare le nuove route")
        print("2. Verificare che il router sia incluso in api/__init__.py")
        print("3. Controllare eventuali errori di import")
        return False
    
    print("\n✅ Route di produttività trovate e caricate!")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Diagnosi completata con problemi")
    else:
        print("\n✅ Diagnosi completata con successo")
