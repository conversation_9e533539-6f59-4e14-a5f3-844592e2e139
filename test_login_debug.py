#!/usr/bin/env python3
"""
Script per testare e debuggare il sistema di login.
"""

import requests
import json
import time

def test_backend_health():
    """Testa la salute del backend."""
    try:
        print("🔍 Test Backend Health...")
        response = requests.get("http://localhost:8001/api/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend OK: {data.get('status')}")
            print(f"   Database: {data.get('database')}")
            return True
        else:
            print(f"❌ Backend Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Backend non raggiungibile: {str(e)}")
        return False

def test_login_admin():
    """Testa il login amministratore."""
    try:
        print("\n🔐 Test Login Amministratore...")
        
        # Prepara i dati del form
        login_data = {
            "username": "admin",
            "password": "admin"
        }
        
        # Effettua la richiesta di login
        response = requests.post(
            "http://localhost:8001/api/auth/login", 
            data=login_data,  # Usa data invece di json per form-data
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login amministratore riuscito!")
            print(f"   Token: {data.get('access_token', 'N/A')[:50]}...")
            print(f"   User ID: {data.get('user_id')}")
            print(f"   Username: {data.get('username')}")
            print(f"   Role: {data.get('role')}")
            return data.get('access_token')
        else:
            print(f"❌ Login fallito: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Errore: {error_data}")
            except:
                print(f"   Errore: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Errore durante login: {str(e)}")
        return None

def test_token_verification(token):
    """Testa la verifica del token."""
    try:
        print("\n🔍 Test Verifica Token...")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            "http://localhost:8001/api/auth/test-token",
            headers=headers,
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Token valido!")
            print(f"   User ID: {data.get('user_id')}")
            print(f"   Username: {data.get('username')}")
            print(f"   Role: {data.get('role')}")
            return True
        else:
            print(f"❌ Token non valido: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Errore: {error_data}")
            except:
                print(f"   Errore: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante verifica token: {str(e)}")
        return False

def test_cors_headers():
    """Testa gli header CORS."""
    try:
        print("\n🌐 Test CORS Headers...")
        
        # Test preflight request
        response = requests.options(
            "http://localhost:8001/api/auth/login",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            },
            timeout=10
        )
        
        print(f"Preflight Status Code: {response.status_code}")
        print("CORS Headers:")
        for header, value in response.headers.items():
            if 'access-control' in header.lower():
                print(f"   {header}: {value}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Errore durante test CORS: {str(e)}")
        return False

def test_frontend_connectivity():
    """Testa la connettività del frontend."""
    try:
        print("\n🌐 Test Frontend Connectivity...")
        
        response = requests.get("http://localhost:3000", timeout=10)
        
        if response.status_code == 200:
            print("✅ Frontend raggiungibile")
            return True
        else:
            print(f"❌ Frontend Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Frontend non raggiungibile: {str(e)}")
        return False

def main():
    """Funzione principale."""
    print("🚀 DEBUG SISTEMA LOGIN")
    print("=" * 50)
    
    # Test 1: Backend Health
    backend_ok = test_backend_health()
    
    if not backend_ok:
        print("\n❌ Backend non funzionante. Impossibile continuare i test.")
        return False
    
    # Test 2: CORS Headers
    cors_ok = test_cors_headers()
    
    # Test 3: Login Admin
    token = test_login_admin()
    
    if not token:
        print("\n❌ Login fallito. Impossibile continuare i test.")
        return False
    
    # Test 4: Token Verification
    token_ok = test_token_verification(token)
    
    # Test 5: Frontend Connectivity
    frontend_ok = test_frontend_connectivity()
    
    # Riepilogo
    print("\n📋 RIEPILOGO TEST:")
    print(f"   Backend:     {'✅ OK' if backend_ok else '❌ ERRORE'}")
    print(f"   CORS:        {'✅ OK' if cors_ok else '❌ ERRORE'}")
    print(f"   Login:       {'✅ OK' if token else '❌ ERRORE'}")
    print(f"   Token:       {'✅ OK' if token_ok else '❌ ERRORE'}")
    print(f"   Frontend:    {'✅ OK' if frontend_ok else '❌ ERRORE'}")
    
    all_ok = backend_ok and token and token_ok and frontend_ok
    
    print(f"\n🎯 STATO GENERALE: {'✅ SISTEMA FUNZIONANTE' if all_ok else '❌ PROBLEMI RILEVATI'}")
    
    if all_ok:
        print("\n🎉 SISTEMA LOGIN FUNZIONANTE!")
        print("Puoi ora accedere al frontend:")
        print("- URL: http://localhost:3000/login")
        print("- Username: admin")
        print("- Password: admin")
    else:
        print("\n🔧 AZIONI RICHIESTE:")
        if not backend_ok:
            print("- Verificare che il backend sia in esecuzione")
        if not cors_ok:
            print("- Verificare configurazione CORS")
        if not token:
            print("- Verificare credenziali di login")
        if not token_ok:
            print("- Verificare sistema di autenticazione")
        if not frontend_ok:
            print("- Verificare che il frontend sia in esecuzione")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
