import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Paper,
  Chip,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  StepContent
} from '@mui/material';
import {
  Assignment,
  Person,
  Schedule,
  Engineering,
  Save,
  Cancel,
  CheckCircle,
  Construction
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import axiosInstance from '../../services/axiosConfig';

const WorkLogForm = ({ onSubmit, onCancel, initialData = null }) => {
  const { selectedCantiere: authSelectedCantiere } = useAuth();

  // Recupera l'ID del cantiere attivo dal contesto di autenticazione o dal localStorage
  const currentCantiereId = authSelectedCantiere?.id_cantiere ||
                           parseInt(localStorage.getItem('selectedCantiereId'), 10) ||
                           null;
  const currentCantiereName = authSelectedCantiere?.commessa ||
                             localStorage.getItem('selectedCantiereName') ||
                             'Cantiere non selezionato';

  const [formData, setFormData] = useState({
    operator_id: '',
    cable_type_id: '',
    activity_type: 'Posa',
    sub_activity_detail: '',
    environmental_conditions: 'Normale',
    tools_used: 'Manuale',
    quantity: '',
    start_timestamp: '',
    end_timestamp: '',
    number_of_operators_on_task: 1,
    notes: '',
    id_cantiere: currentCantiereId || '' // Usa il cantiere attivo
  });

  const [operators, setOperators] = useState([]);
  const [cableTypes, setCableTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Opzioni per i dropdown
  const activityTypes = [
    { value: 'Posa', label: 'Posa' },
    { value: 'Collegamento', label: 'Collegamento' },
    { value: 'Certificazione', label: 'Certificazione' }
  ];

  const environmentalConditions = [
    { value: 'Normale', label: 'Normale' },
    { value: 'Spazi Ristretti', label: 'Spazi Ristretti' },
    { value: 'In Altezza', label: 'In Altezza' },
    { value: 'Esterno', label: 'Esterno' }
  ];

  const toolsUsed = [
    { value: 'Manuale', label: 'Manuale' },
    { value: 'Automatico', label: 'Automatico' }
  ];

  useEffect(() => {
    loadInitialData();
    if (initialData) {
      setFormData({
        ...initialData,
        start_timestamp: formatDateTimeLocal(initialData.start_timestamp),
        end_timestamp: formatDateTimeLocal(initialData.end_timestamp)
      });
    }
  }, [initialData]);

  const formatDateTimeLocal = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toISOString().slice(0, 16);
  };

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Carica operatori e tipi di cavo (non i cantieri perché usiamo quello attivo)
      const [operatorsRes, cableTypesRes] = await Promise.all([
        axiosInstance.get('/responsabili'),
        axiosInstance.get('/admin/tipologie-cavi')
      ]);

      setOperators(operatorsRes.data || []);
      setCableTypes(cableTypesRes.data || []);

      // Assicurati che il cantiere attivo sia impostato nel form
      if (currentCantiereId) {
        setFormData(prev => ({ ...prev, id_cantiere: currentCantiereId }));
      }

    } catch (error) {
      console.error('Errore nel caricamento dati iniziali:', error);
      setErrors({ general: 'Errore nel caricamento dei dati' });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || '' : value
    }));
    
    // Rimuovi errore per questo campo
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.operator_id) newErrors.operator_id = 'Operatore richiesto';
    if (!formData.activity_type) newErrors.activity_type = 'Tipo attività richiesto';
    if (!formData.quantity || formData.quantity <= 0) newErrors.quantity = 'Quantità deve essere maggiore di 0';
    if (!formData.start_timestamp) newErrors.start_timestamp = 'Data/ora inizio richiesta';
    if (!formData.end_timestamp) newErrors.end_timestamp = 'Data/ora fine richiesta';
    if (!formData.id_cantiere) newErrors.id_cantiere = 'Cantiere richiesto';
    if (!formData.number_of_operators_on_task || formData.number_of_operators_on_task < 1) {
      newErrors.number_of_operators_on_task = 'Numero operatori deve essere almeno 1';
    }

    // Valida che end_timestamp sia dopo start_timestamp
    if (formData.start_timestamp && formData.end_timestamp) {
      const start = new Date(formData.start_timestamp);
      const end = new Date(formData.end_timestamp);
      if (end <= start) {
        newErrors.end_timestamp = 'Data/ora fine deve essere dopo inizio';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      
      // Prepara i dati per l'invio
      const submitData = {
        ...formData,
        operator_id: parseInt(formData.operator_id),
        cable_type_id: formData.cable_type_id ? parseInt(formData.cable_type_id) : null,
        quantity: parseFloat(formData.quantity),
        number_of_operators_on_task: parseInt(formData.number_of_operators_on_task),
        id_cantiere: parseInt(formData.id_cantiere),
        start_timestamp: new Date(formData.start_timestamp).toISOString(),
        end_timestamp: new Date(formData.end_timestamp).toISOString()
      };

      await onSubmit(submitData);
      
    } catch (error) {
      console.error('Errore nell\'invio del form:', error);
      setErrors({ 
        general: error.response?.data?.detail || 'Errore nell\'invio del work log' 
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading && !operators.length) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <CircularProgress sx={{ mb: 2 }} />
        <Typography variant="h6" color="text.secondary">
          Caricamento dati...
        </Typography>
      </Box>
    );
  }

  // Se non c'è un cantiere attivo, mostra un messaggio
  if (!currentCantiereId) {
    return (
      <Container maxWidth="lg" sx={{ py: 3 }}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            Nessun cantiere attivo selezionato
          </Typography>
          <Typography variant="body2">
            Per creare un work log, seleziona prima un cantiere dalla dashboard principale.
          </Typography>
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header con cantiere attivo */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>
          <Assignment sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 700 }}>
              {initialData ? 'Modifica Work Log' : 'Nuovo Work Log'}
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 1 }}>
              <Construction sx={{ fontSize: 20, color: 'text.secondary', mr: 1 }} />
              <Typography variant="subtitle1" color="text.secondary">
                Cantiere: <strong>{currentCantiereName}</strong>
              </Typography>
              <Chip
                label={`ID: ${currentCantiereId}`}
                size="small"
                color="primary"
                variant="outlined"
                sx={{ ml: 2 }}
              />
            </Box>
          </Box>
        </Box>
        <Typography variant="subtitle1" color="text.secondary">
          Registra i dettagli del lavoro svolto per il monitoraggio della produttività
        </Typography>
      </Box>

      {errors.general && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errors.general}
        </Alert>
      )}

      <Card elevation={3}>
        <CardContent sx={{ p: 4 }}>
          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              {/* Sezione Informazioni Base */}
              <Grid item xs={12}>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <Person sx={{ mr: 1, color: 'primary.main' }} />
                  Informazioni Base
                </Typography>
                <Divider sx={{ mb: 3 }} />
              </Grid>

              {/* Operatore */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={!!errors.operator_id}>
                  <InputLabel>Operatore *</InputLabel>
                  <Select
                    name="operator_id"
                    value={formData.operator_id}
                    onChange={handleInputChange}
                    label="Operatore *"
                  >
                    <MenuItem value="">Seleziona operatore</MenuItem>
                    {operators.map(op => (
                      <MenuItem key={op.id_responsabile} value={op.id_responsabile}>
                        {op.nome_responsabile} ({op.experience_level || 'Senior'})
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.operator_id && (
                    <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                      {errors.operator_id}
                    </Typography>
                  )}
                </FormControl>
              </Grid>

              {/* Cantiere (sola lettura) */}
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Cantiere"
                  value={currentCantiereName}
                  InputProps={{
                    readOnly: true,
                    startAdornment: <Construction sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                  variant="filled"
                  sx={{ bgcolor: 'grey.50' }}
                />
              </Grid>

              {/* Tipo di Attività */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth error={!!errors.activity_type}>
                  <InputLabel>Tipo Attività *</InputLabel>
                  <Select
                    name="activity_type"
                    value={formData.activity_type}
                    onChange={handleInputChange}
                    label="Tipo Attività *"
                  >
                    {activityTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.activity_type && (
                    <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                      {errors.activity_type}
                    </Typography>
                  )}
                </FormControl>
              </Grid>

              {/* Tipo di Cavo */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Tipo di Cavo</InputLabel>
                  <Select
                    name="cable_type_id"
                    value={formData.cable_type_id}
                    onChange={handleInputChange}
                    label="Tipo di Cavo"
                  >
                    <MenuItem value="">Seleziona tipo di cavo (opzionale)</MenuItem>
                    {cableTypes.map(type => (
                      <MenuItem key={type.id_tipologia} value={type.id_tipologia}>
                        {type.codice_prodotto} - {type.nome_commerciale}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Sezione Dettagli Lavoro */}
              <Grid item xs={12}>
                <Typography variant="h6" sx={{ mb: 2, mt: 2, display: 'flex', alignItems: 'center' }}>
                  <Engineering sx={{ mr: 1, color: 'primary.main' }} />
                  Dettagli del Lavoro
                </Typography>
                <Divider sx={{ mb: 3 }} />
              </Grid>

              {/* Dettaglio Sub-Attività */}
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  name="sub_activity_detail"
                  label="Dettaglio Attività"
                  value={formData.sub_activity_detail}
                  onChange={handleInputChange}
                  placeholder="es. Posa in canalina a vista"
                />
              </Grid>

              {/* Quantità */}
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="number"
                  name="quantity"
                  label={`Quantità * ${formData.activity_type === 'Posa' ? '(metri)' : '(unità)'}`}
                  value={formData.quantity}
                  onChange={handleInputChange}
                  error={!!errors.quantity}
                  helperText={errors.quantity}
                  inputProps={{ step: 0.1, min: 0 }}
                />
              </Grid>

              {/* Condizioni Ambientali */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Condizioni Ambientali</InputLabel>
                  <Select
                    name="environmental_conditions"
                    value={formData.environmental_conditions}
                    onChange={handleInputChange}
                    label="Condizioni Ambientali"
                  >
                    {environmentalConditions.map(condition => (
                      <MenuItem key={condition.value} value={condition.value}>
                        {condition.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Strumenti Utilizzati */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Strumenti Utilizzati</InputLabel>
                  <Select
                    name="tools_used"
                    value={formData.tools_used}
                    onChange={handleInputChange}
                    label="Strumenti Utilizzati"
                  >
                    {toolsUsed.map(tool => (
                      <MenuItem key={tool.value} value={tool.value}>
                        {tool.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Sezione Tempi */}
              <Grid item xs={12}>
                <Typography variant="h6" sx={{ mb: 2, mt: 2, display: 'flex', alignItems: 'center' }}>
                  <Schedule sx={{ mr: 1, color: 'primary.main' }} />
                  Tempi di Lavoro
                </Typography>
                <Divider sx={{ mb: 3 }} />
              </Grid>

              {/* Data/Ora Inizio */}
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="datetime-local"
                  name="start_timestamp"
                  label="Data/Ora Inizio *"
                  value={formData.start_timestamp}
                  onChange={handleInputChange}
                  error={!!errors.start_timestamp}
                  helperText={errors.start_timestamp}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              {/* Data/Ora Fine */}
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="datetime-local"
                  name="end_timestamp"
                  label="Data/Ora Fine *"
                  value={formData.end_timestamp}
                  onChange={handleInputChange}
                  error={!!errors.end_timestamp}
                  helperText={errors.end_timestamp}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              {/* Numero Operatori */}
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="number"
                  name="number_of_operators_on_task"
                  label="Numero Operatori *"
                  value={formData.number_of_operators_on_task}
                  onChange={handleInputChange}
                  error={!!errors.number_of_operators_on_task}
                  helperText={errors.number_of_operators_on_task}
                  inputProps={{ min: 1 }}
                />
              </Grid>

              {/* Note */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  name="notes"
                  label="Note"
                  value={formData.notes}
                  onChange={handleInputChange}
                  placeholder="Note aggiuntive sul lavoro svolto..."
                />
              </Grid>

              {/* Pulsanti */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, pt: 3 }}>
                  <Button
                    variant="outlined"
                    onClick={onCancel}
                    startIcon={<Cancel />}
                    size="large"
                  >
                    Annulla
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <Save />}
                    size="large"
                    sx={{
                      background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                      '&:hover': {
                        background: 'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',
                      }
                    }}
                  >
                    {loading ? 'Salvando...' : (initialData ? 'Aggiorna Work Log' : 'Crea Work Log')}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default WorkLogForm;
